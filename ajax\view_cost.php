<?php
// /ajax/view_cost.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

// Debug: Log request
error_log("View cost request: " . print_r($_GET, true));

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $id = intval($_GET['id'] ?? 0);

    if ($id > 0) {
        try {
            $stmt = $pdo->prepare("
                SELECT * FROM costs
                WHERE id = ?
            ");
            $stmt->execute([$id]);
            $cost = $stmt->fetch(PDO::FETCH_ASSOC); // Ensure we get an associative array

            if ($cost) {
                // Debug: Log the cost data
                error_log("Cost data found: " . print_r($cost, true));

                // Convert numeric values to appropriate types
                $cost['id'] = intval($cost['id']);
                $cost['amount'] = floatval($cost['amount']);
                $cost['num_days'] = intval($cost['num_days']);
                if (isset($cost['category_id'])) {
                    $cost['category_id'] = intval($cost['category_id']);
                }
                if (isset($cost['subcategory_id'])) {
                    $cost['subcategory_id'] = intval($cost['subcategory_id']);
                }

                echo json_encode([
                    'success' => true,
                    'cost' => $cost
                ]);
            } else {
                error_log("Cost not found for ID: $id");
                echo json_encode([
                    'success' => false,
                    'message' => 'Cost not found'
                ]);
            }
        } catch (Exception $e) {
            error_log("Error fetching cost: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => 'Error fetching cost: ' . $e->getMessage()
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid cost ID'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
}
?>
