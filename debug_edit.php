<?php
// Debug script for edit functionality
header('Content-Type: text/html');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Edit Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        h1, h2 {
            color: #4361ee;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .test-form {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 15px;
            background-color: #4361ee;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e2f0fb;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug Edit Functionality</h1>
        
        <div class="card">
            <h2>1. Check Database Connection</h2>
            <?php
            require_once __DIR__ . '/config/db.php';
            echo "<p>Database connection established.</p>";
            ?>
        </div>
        
        <div class="card">
            <h2>2. Check Cost Table Structure</h2>
            <?php
            try {
                $stmt = $pdo->query("DESCRIBE costs");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<table>";
                echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
                foreach ($columns as $column) {
                    echo "<tr>";
                    foreach ($column as $key => $value) {
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            } catch (Exception $e) {
                echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            }
            ?>
        </div>
        
        <div class="card">
            <h2>3. Test Edit Form</h2>
            <div class="test-form">
                <form id="test-edit-form">
                    <div class="form-group">
                        <label for="cost-id">Cost ID:</label>
                        <input type="number" id="cost-id" name="id" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description:</label>
                        <input type="text" id="description" name="description" required>
                    </div>
                    <div class="form-group">
                        <label for="amount">Amount:</label>
                        <input type="number" id="amount" name="amount" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="rate-type">Rate Type:</label>
                        <select id="rate-type" name="rate_type">
                            <option value="daily">Daily</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="num-days">Number of Days:</label>
                        <input type="number" id="num-days" name="num_days" min="1" value="1" required>
                    </div>
                    <button type="submit">Test Update</button>
                </form>
                <div id="test-result" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('test-edit-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            const resultDiv = document.getElementById('test-result');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'Sending request...';
            
            try {
                const formData = new FormData(this);
                const response = await fetch('ajax/update_cost.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h3>Response:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <pre>${error.message}</pre>
                `;
            }
        });
    </script>
</body>
</html>
