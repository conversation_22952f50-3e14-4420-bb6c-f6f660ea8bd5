/**
 * Navigation Dropdown Menu Functionality
 * Handles the costs category dropdown menu in the navigation
 */

document.addEventListener("DOMContentLoaded", () => {
  initializeDropdownMenu();
});

function initializeDropdownMenu() {
  const savedCostsDropdown = document.querySelector('.saved-costs-dropdown');
  const dropdownBtn = document.querySelector('.dropdown-btn');
  const dropdownMenu = document.querySelector('.dropdown-menu');

  if (!savedCostsDropdown || !dropdownBtn || !dropdownMenu) {
    return; // No dropdown found, exit gracefully
  }

  // Toggle dropdown on click
  dropdownBtn.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    savedCostsDropdown.classList.toggle('open');
  });

  // Close dropdown when clicking outside
  document.addEventListener('click', (e) => {
    if (!savedCostsDropdown.contains(e.target)) {
      savedCostsDropdown.classList.remove('open');
    }
  });

  // Handle category and subcategory clicks
  const categoryLinks = document.querySelectorAll('.dropdown-category-link');
  const subcategoryLinks = document.querySelectorAll('.dropdown-subcategory-link');
  const clearFiltersLinks = document.querySelectorAll('.clear-filters-link');

  // Clear filters link clicks
  clearFiltersLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();

      // Check if we're on viewer dashboard
      if (window.location.pathname.includes('viewer_dashboard.php')) {
        clearCostFilters();
      } else {
        // For admin costs page, scroll to top and show all categories
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });

        // Show all category sections
        const categorySections = document.querySelectorAll('.category-costs-section');
        categorySections.forEach(section => {
          section.style.display = 'block';
        });

        // Show all subcategory sections
        const subcategorySections = document.querySelectorAll('.subcategory-costs-section');
        subcategorySections.forEach(section => {
          section.style.display = 'block';
        });
      }

      savedCostsDropdown.classList.remove('open');
    });
  });

  // Category link clicks
  categoryLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const categoryId = link.dataset.filterCategory;

      // Skip if this is a clear filters link
      if (link.classList.contains('clear-filters-link')) {
        return;
      }

      if (categoryId) {
        // Check if we're on viewer dashboard
        if (window.location.pathname.includes('viewer_dashboard.php')) {
          filterCostsByCategory(categoryId);
        } else {
          // For admin costs page, scroll to category section and highlight it
          const categorySection = document.querySelector(`#category-${categoryId}`);
          if (categorySection) {
            // Hide all other categories
            const allCategorySections = document.querySelectorAll('.category-costs-section');
            allCategorySections.forEach(section => {
              section.style.display = 'none';
            });

            // Show only the selected category
            categorySection.style.display = 'block';

            // Show all subcategories in this category
            const subcategorySections = categorySection.querySelectorAll('.subcategory-costs-section');
            subcategorySections.forEach(section => {
              section.style.display = 'block';
            });

            // Scroll to the category
            categorySection.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });

            // Add a temporary highlight effect
            categorySection.style.border = '2px solid var(--primary)';
            setTimeout(() => {
              categorySection.style.border = '';
            }, 3000);
          }
        }
      }

      savedCostsDropdown.classList.remove('open');
    });
  });

  // Subcategory link clicks
  subcategoryLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const subcategoryId = link.dataset.filterSubcategory;
      
      if (subcategoryId) {
        // Check if we're on viewer dashboard
        if (window.location.pathname.includes('viewer_dashboard.php')) {
          filterCostsBySubcategory(subcategoryId);
        } else {
          // For admin costs page, scroll to subcategory section and highlight it
          const subcategorySection = document.querySelector(`#subcategory-${subcategoryId}`);
          if (subcategorySection) {
            // Hide all categories first
            const allCategorySections = document.querySelectorAll('.category-costs-section');
            allCategorySections.forEach(section => {
              section.style.display = 'none';
            });

            // Show the parent category of this subcategory
            const parentCategory = subcategorySection.closest('.category-costs-section');
            if (parentCategory) {
              parentCategory.style.display = 'block';

              // Hide all subcategories in this category
              const allSubcategories = parentCategory.querySelectorAll('.subcategory-costs-section');
              allSubcategories.forEach(sub => {
                sub.style.display = 'none';
              });

              // Show only the selected subcategory
              subcategorySection.style.display = 'block';
            }

            // Scroll to the subcategory
            subcategorySection.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });

            // Add a temporary highlight effect
            subcategorySection.style.border = '2px solid var(--primary)';
            setTimeout(() => {
              subcategorySection.style.border = '';
            }, 3000);
          }
        }
      }

      savedCostsDropdown.classList.remove('open');
    });
  });

  // Keyboard navigation
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      savedCostsDropdown.classList.remove('open');
      dropdownBtn.focus();
    }

    // Arrow key navigation within dropdown
    if (savedCostsDropdown.classList.contains('open')) {
      const focusableElements = dropdownMenu.querySelectorAll('a');
      const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);

      if (e.key === 'ArrowDown') {
        e.preventDefault();
        const nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
        focusableElements[nextIndex].focus();
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
        focusableElements[prevIndex].focus();
      } else if (e.key === 'Enter' || e.key === ' ') {
        if (document.activeElement.classList.contains('dropdown-category-link') ||
            document.activeElement.classList.contains('dropdown-subcategory-link')) {
          e.preventDefault();
          document.activeElement.click();
        }
      }
    }
  });

  // Add accessibility support
  dropdownBtn.setAttribute('aria-expanded', 'false');
  dropdownBtn.setAttribute('aria-haspopup', 'true');

  // Update aria-expanded when dropdown opens/closes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const isOpen = savedCostsDropdown.classList.contains('open');
        dropdownBtn.setAttribute('aria-expanded', isOpen.toString());
      }
    });
  });

  observer.observe(savedCostsDropdown, { attributes: true });
}

// Filter functions for viewer dashboard
function filterCostsByCategory(categoryId) {
  const costTable = document.getElementById('cost-table');
  if (!costTable) return;

  // Clear any existing search
  const searchInput = document.querySelector('.table-search[data-target="cost-table"]');
  if (searchInput) {
    searchInput.value = '';
  }

  // Get the category name from the dropdown
  const categoryLink = document.querySelector(`[data-filter-category="${categoryId}"]`);
  const categoryName = categoryLink ? categoryLink.textContent.trim().replace(/^\s*\S+\s*/, '') : '';

  const rows = costTable.querySelectorAll('tbody tr');
  let visibleCount = 0;

  rows.forEach(row => {
    const categoryCell = row.cells[2]; // Category column (index 2)
    if (categoryCell) {
      const cellText = categoryCell.textContent.trim();
      if (cellText === categoryName || categoryName === '') {
        row.style.display = 'table-row';
        visibleCount++;
      } else {
        row.style.display = 'none';
      }
    }
  });

  // Show a toast notification about the filter
  if (typeof showToast === 'function') {
    showToast(`Showing costs for "${categoryName}" (${visibleCount} entries)`, 'info', 3000);
  }
}

function filterCostsBySubcategory(subcategoryId) {
  const costTable = document.getElementById('cost-table');
  if (!costTable) return;

  // Clear any existing search
  const searchInput = document.querySelector('.table-search[data-target="cost-table"]');
  if (searchInput) {
    searchInput.value = '';
  }

  // Get the subcategory name from the dropdown
  const subcategoryLink = document.querySelector(`[data-filter-subcategory="${subcategoryId}"]`);
  const subcategoryName = subcategoryLink ? subcategoryLink.textContent.trim().replace(/^\s*\S+\s*/, '') : '';

  const rows = costTable.querySelectorAll('tbody tr');
  let visibleCount = 0;

  rows.forEach(row => {
    const subcategoryCell = row.cells[3]; // Subcategory column (index 3)
    if (subcategoryCell) {
      const cellText = subcategoryCell.textContent.trim();
      if (cellText === subcategoryName || subcategoryName === '') {
        row.style.display = 'table-row';
        visibleCount++;
      } else {
        row.style.display = 'none';
      }
    }
  });

  // Show a toast notification about the filter
  if (typeof showToast === 'function') {
    showToast(`Showing costs for "${subcategoryName}" (${visibleCount} entries)`, 'info', 3000);
  }
}

// Function to clear all filters
function clearCostFilters() {
  const costTable = document.getElementById('cost-table');
  if (!costTable) return;

  const rows = costTable.querySelectorAll('tbody tr');
  rows.forEach(row => {
    row.style.display = 'table-row';
  });

  // Clear search input
  const searchInput = document.querySelector('.table-search[data-target="cost-table"]');
  if (searchInput) {
    searchInput.value = '';
  }

  if (typeof showToast === 'function') {
    showToast('All filters cleared', 'info', 2000);
  }
}
