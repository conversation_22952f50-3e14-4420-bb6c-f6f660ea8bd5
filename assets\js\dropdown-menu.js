/**
 * Navigation Dropdown Menu Functionality
 * Handles the costs category dropdown menu in the navigation
 */

document.addEventListener("DOMContentLoaded", () => {
  initializeDropdownMenu();
});

function initializeDropdownMenu() {
  const navDropdown = document.querySelector('.nav-dropdown');
  const dropdownLink = document.querySelector('.nav-link-with-dropdown');
  const dropdownMenu = document.querySelector('.dropdown-menu');

  if (!navDropdown || !dropdownLink || !dropdownMenu) {
    return; // No dropdown found, exit gracefully
  }

  // Toggle dropdown on click
  dropdownLink.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    navDropdown.classList.toggle('open');
  });

  // Close dropdown when clicking outside
  document.addEventListener('click', (e) => {
    if (!navDropdown.contains(e.target)) {
      navDropdown.classList.remove('open');
    }
  });

  // Handle category and subcategory clicks
  const categoryLinks = document.querySelectorAll('.dropdown-category-link');
  const subcategoryLinks = document.querySelectorAll('.dropdown-subcategory-link');
  const clearFiltersLinks = document.querySelectorAll('.clear-filters-link');

  // Clear filters link clicks
  clearFiltersLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();

      // For viewer dashboard, clear all filters
      if (window.location.pathname.includes('viewer_dashboard.php')) {
        clearCostFilters();
        // Show costs tab if using tab navigation
        if (typeof showTab === 'function') {
          showTab('#costs');
        }
      } else {
        // For admin costs page, scroll to top
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }

      navDropdown.classList.remove('open');
    });
  });

  // Category link clicks
  categoryLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const categoryId = link.dataset.filterCategory;

      // Skip if this is a clear filters link
      if (link.classList.contains('clear-filters-link')) {
        return;
      }

      if (categoryId) {
        // For viewer dashboard, filter the table
        if (window.location.pathname.includes('viewer_dashboard.php')) {
          filterCostsByCategory(categoryId);
          // Show costs tab if using tab navigation
          if (typeof showTab === 'function') {
            showTab('#costs');
          }
        } else {
          // For admin costs page, scroll to category section
          const categorySection = document.querySelector(`#category-${categoryId}`);
          if (categorySection) {
            categorySection.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        }
      }

      navDropdown.classList.remove('open');
    });
  });

  // Subcategory link clicks
  subcategoryLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const subcategoryId = link.dataset.filterSubcategory;
      
      if (subcategoryId) {
        // For viewer dashboard, filter the table
        if (window.location.pathname.includes('viewer_dashboard.php')) {
          filterCostsBySubcategory(subcategoryId);
          // Show costs tab if using tab navigation
          if (typeof showTab === 'function') {
            showTab('#costs');
          }
        } else {
          // For admin costs page, scroll to subcategory section
          const subcategorySection = document.querySelector(`#subcategory-${subcategoryId}`);
          if (subcategorySection) {
            subcategorySection.scrollIntoView({ 
              behavior: 'smooth',
              block: 'start'
            });
          }
        }
      }
      
      navDropdown.classList.remove('open');
    });
  });

  // Keyboard navigation
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      navDropdown.classList.remove('open');
      dropdownLink.focus();
    }

    // Arrow key navigation within dropdown
    if (navDropdown.classList.contains('open')) {
      const focusableElements = dropdownMenu.querySelectorAll('a');
      const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);

      if (e.key === 'ArrowDown') {
        e.preventDefault();
        const nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
        focusableElements[nextIndex].focus();
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
        focusableElements[prevIndex].focus();
      } else if (e.key === 'Enter' || e.key === ' ') {
        if (document.activeElement.classList.contains('dropdown-category-link') ||
            document.activeElement.classList.contains('dropdown-subcategory-link')) {
          e.preventDefault();
          document.activeElement.click();
        }
      }
    }
  });

  // Add accessibility support
  dropdownLink.setAttribute('aria-expanded', 'false');
  dropdownLink.setAttribute('aria-haspopup', 'true');
  
  // Update aria-expanded when dropdown opens/closes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const isOpen = navDropdown.classList.contains('open');
        dropdownLink.setAttribute('aria-expanded', isOpen.toString());
      }
    });
  });
  
  observer.observe(navDropdown, { attributes: true });
}

// Filter functions for viewer dashboard
function filterCostsByCategory(categoryId) {
  const costTable = document.getElementById('cost-table');
  if (!costTable) return;

  // Clear any existing search
  const searchInput = document.querySelector('.table-search[data-target="cost-table"]');
  if (searchInput) {
    searchInput.value = '';
  }

  const rows = costTable.querySelectorAll('tbody tr');
  let visibleCount = 0;

  rows.forEach(row => {
    const categoryCell = row.cells[2]; // Category column (index 2)
    if (categoryCell) {
      // Show all rows for now - you can implement specific filtering logic here
      // based on how category data is stored in the table
      row.style.display = 'table-row';
      visibleCount++;
    }
  });

  // Show a toast notification about the filter
  if (typeof showToast === 'function') {
    showToast(`Showing costs for selected category (${visibleCount} entries)`, 'info', 3000);
  }
}

function filterCostsBySubcategory(subcategoryId) {
  const costTable = document.getElementById('cost-table');
  if (!costTable) return;

  // Clear any existing search
  const searchInput = document.querySelector('.table-search[data-target="cost-table"]');
  if (searchInput) {
    searchInput.value = '';
  }

  const rows = costTable.querySelectorAll('tbody tr');
  let visibleCount = 0;

  rows.forEach(row => {
    const subcategoryCell = row.cells[3]; // Subcategory column (index 3)
    if (subcategoryCell) {
      // Show all rows for now - you can implement specific filtering logic here
      // based on how subcategory data is stored in the table
      row.style.display = 'table-row';
      visibleCount++;
    }
  });

  // Show a toast notification about the filter
  if (typeof showToast === 'function') {
    showToast(`Showing costs for selected subcategory (${visibleCount} entries)`, 'info', 3000);
  }
}

// Function to clear all filters
function clearCostFilters() {
  const costTable = document.getElementById('cost-table');
  if (!costTable) return;

  const rows = costTable.querySelectorAll('tbody tr');
  rows.forEach(row => {
    row.style.display = 'table-row';
  });

  // Clear search input
  const searchInput = document.querySelector('.table-search[data-target="cost-table"]');
  if (searchInput) {
    searchInput.value = '';
  }

  if (typeof showToast === 'function') {
    showToast('All filters cleared', 'info', 2000);
  }
}
