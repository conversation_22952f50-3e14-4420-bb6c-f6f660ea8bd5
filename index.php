<?php
// Check if database setup is needed
$setupNeeded = false;

// Try to connect to the database
$host = 'localhost';
$db   = 'cost_calculator';
$user = 'root';
$pass = '';

// Check if any MySQL extensions are available
if (!extension_loaded('pdo_mysql') && !extension_loaded('mysqli')) {
    // Display error about missing extensions
    echo '<div style="background-color: #f8d7da; color: #721c24; padding: 20px; margin: 20px; border-radius: 5px; border: 1px solid #f5c6cb;">';
    echo '<h2>Database Connection Error</h2>';
    echo '<p>Your PHP installation is missing required MySQL extensions.</p>';
    echo '<h3>How to fix this:</h3>';
    echo '<ol>';
    echo '<li><strong>Enable PHP Extensions:</strong> You need to enable either the <code>pdo_mysql</code> or <code>mysqli</code> extension in your PHP configuration.</li>';
    echo '<li><strong>Edit php.ini:</strong>';
    echo '<ul>';
    echo '<li>Find your php.ini file (usually in C:\xampp\php\ or C:\wamp64\bin\php\php[version]\)</li>';
    echo '<li>Uncomment these lines by removing the semicolon (;) at the beginning:';
    echo '<pre>';
    echo ';extension=mysqli<br>';
    echo ';extension=pdo_mysql';
    echo '</pre></li>';
    echo '<li>Save the file and restart your web server</li>';
    echo '</ul></li>';
    echo '</ol>';
    echo '<p>Once you\'ve enabled the extensions, refresh this page.</p>';
    echo '</div>';
    exit;
}

// Try to connect using PDO if available
if (extension_loaded('pdo_mysql')) {
    try {
        $pdo = new PDO("mysql:host=$host;dbname=$db", $user, $pass);
        // If we get here, the database exists and we can connect
        $setupNeeded = false;
    } catch (PDOException $e) {
        // If we can't connect, we need to run setup
        $setupNeeded = true;
    }
}
// Try to connect using mysqli if PDO is not available
elseif (extension_loaded('mysqli')) {
    $mysqli = @new mysqli($host, $user, $pass, $db);
    if ($mysqli->connect_error) {
        // If we can't connect, we need to run setup
        $setupNeeded = true;
    } else {
        // If we can connect, we don't need setup
        $setupNeeded = false;
        $mysqli->close();
    }
}

// Redirect based on setup status
if ($setupNeeded) {
    // Show setup options
    echo '<div style="font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">';
    echo '<h1>Learnership Cost & Invoice Management System</h1>';
    echo '<p>Welcome! It looks like you need to set up the database before using the application.</p>';

    echo '<div style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">';
    echo '<h2>Setup Options</h2>';
    echo '<p>Click one of the following links to set up your database:</p>';

    echo '<ul style="list-style-type: none; padding: 0;">';
    echo '<li style="margin-bottom: 10px;"><a href="setup.php" style="display: inline-block; padding: 10px 15px; background-color: #4361ee; color: white; text-decoration: none; border-radius: 5px;">Run Setup Script</a> - Create the database and tables</li>';
    echo '<li style="margin-bottom: 10px;"><a href="phpinfo.php" style="display: inline-block; padding: 10px 15px; background-color: #3a0ca3; color: white; text-decoration: none; border-radius: 5px;">Check PHP Info</a> - View your PHP configuration</li>';
    echo '</ul>';
    echo '</div>';

    echo '<p><strong>Note:</strong> If you encounter any issues, please make sure your web server and MySQL database are running.</p>';
    echo '</div>';
} else {
    // Database exists, redirect to login page
    header('Location: auth/login.php');
    exit;
}
?>
