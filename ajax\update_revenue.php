<?php
// /ajax/update_revenue.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

$id = intval($_POST['id'] ?? 0);
$month_year = trim($_POST['month_year'] ?? '');
$student_type = trim($_POST['student_type'] ?? '');
$revenue_per_student = floatval($_POST['revenue_per_student'] ?? 0);
$total_students = intval($_POST['total_students'] ?? 0);

if ($id && $month_year && $student_type && $revenue_per_student > 0 && $total_students > 0) {
  $stmt = $pdo->prepare("UPDATE revenue SET month_year = ?, student_type = ?, revenue_per_student = ?, total_students = ? WHERE id = ?");
  $stmt->execute([$month_year, $student_type, $revenue_per_student, $total_students, $id]);
  echo json_encode(['success' => true, 'message' => 'Revenue updated successfully.']);
} else {
  echo json_encode(['success' => false, 'message' => 'Invalid input for revenue update.']);
}
?>