<?php
// Simple script to create tables directly
$host = 'sql307.infinityfree.com';
$user = 'if0_39163993';
$pass = 'sV5RSK88Jk5';
$db = 'if0_39163993_cost_calculator';

// Connect directly to MySQL
$mysqli = new mysqli($host, $user, $pass);

// Check connection
if ($mysqli->connect_error) {
    die("Connection failed: " . $mysqli->connect_error);
}

echo "<h1>Creating Database Tables</h1>";

// Create database if it doesn't exist
$mysqli->query("CREATE DATABASE IF NOT EXISTS $db");
echo "<p>Database created or already exists.</p>";

// Select the database
$mysqli->select_db($db);

// Create users table
$sql = "CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'viewer') NOT NULL DEFAULT 'viewer',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($mysqli->query($sql)) {
    echo "<p>Users table created successfully.</p>";
} else {
    echo "<p>Error creating users table: " . $mysqli->error . "</p>";
}

// Create categories table
$sql = "CREATE TABLE IF NOT EXISTS categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($mysqli->query($sql)) {
    echo "<p>Categories table created successfully.</p>";
} else {
    echo "<p>Error creating categories table: " . $mysqli->error . "</p>";
}

// Create subcategories table
$sql = "CREATE TABLE IF NOT EXISTS subcategories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
)";

if ($mysqli->query($sql)) {
    echo "<p>Subcategories table created successfully.</p>";
} else {
    echo "<p>Error creating subcategories table: " . $mysqli->error . "</p>";
}

// Create costs table
$sql = "CREATE TABLE IF NOT EXISTS costs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    description VARCHAR(255) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    category_id INT NOT NULL,
    subcategory_id INT NOT NULL,
    rate_type ENUM('daily', 'monthly') NOT NULL DEFAULT 'daily',
    num_days INT NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id),
    FOREIGN KEY (subcategory_id) REFERENCES subcategories(id)
)";

if ($mysqli->query($sql)) {
    echo "<p>Costs table created successfully.</p>";
} else {
    echo "<p>Error creating costs table: " . $mysqli->error . "</p>";
}

// Create revenue table
$sql = "CREATE TABLE IF NOT EXISTS revenue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    month_year VARCHAR(7) NOT NULL,
    student_type VARCHAR(50) NOT NULL,
    revenue_per_student DECIMAL(10,2) NOT NULL,
    total_students INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($mysqli->query($sql)) {
    echo "<p>Revenue table created successfully.</p>";
} else {
    echo "<p>Error creating revenue table: " . $mysqli->error . "</p>";
}

// Create invoices table
$sql = "CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_name VARCHAR(100) NOT NULL,
    course VARCHAR(100) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    tax DECIMAL(5,2) NOT NULL DEFAULT 0,
    due_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

if ($mysqli->query($sql)) {
    echo "<p>Invoices table created successfully.</p>";
} else {
    echo "<p>Error creating invoices table: " . $mysqli->error . "</p>";
}

// Create default admin user
$check = $mysqli->query("SELECT id FROM users WHERE username = 'admin'");
if ($check->num_rows == 0) {
    $password_hash = password_hash('admin123', PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (username, password, role) VALUES ('admin', '$password_hash', 'admin')";
    
    if ($mysqli->query($sql)) {
        echo "<p>Default admin user created. Username: admin, Password: admin123</p>";
    } else {
        echo "<p>Error creating admin user: " . $mysqli->error . "</p>";
    }
} else {
    echo "<p>Admin user already exists.</p>";
}

// Add sample categories
$check = $mysqli->query("SELECT id FROM categories LIMIT 1");
if ($check->num_rows == 0) {
    $categories = [
        ['Salaries', 'Staff and instructor salaries'],
        ['Facilities', 'Building rent and maintenance'],
        ['Equipment', 'Learning equipment and supplies'],
        ['Marketing', 'Advertising and promotional activities']
    ];
    
    foreach ($categories as $cat) {
        $name = $mysqli->real_escape_string($cat[0]);
        $desc = $mysqli->real_escape_string($cat[1]);
        $sql = "INSERT INTO categories (name, description) VALUES ('$name', '$desc')";
        $mysqli->query($sql);
    }
    
    echo "<p>Sample categories added.</p>";
} else {
    echo "<p>Categories already exist.</p>";
}

// Close connection
$mysqli->close();

echo "<p><strong>Setup complete!</strong> You can now <a href='auth/login.php'>login</a> with username 'admin' and password 'admin123'.</p>";
?>
