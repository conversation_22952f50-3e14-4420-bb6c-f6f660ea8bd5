<?php
// test_ajax.php - Test AJAX endpoints directly
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX Endpoints</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2 {
            color: #333;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        button {
            padding: 8px 16px;
            background-color: #4361ee;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #3a0ca3;
        }
        .result {
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>AJAX Endpoint Tester</h1>
    
    <h2>Categories</h2>
    <button id="test-categories">Test Get Categories</button>
    <div id="categories-result" class="result">
        <pre>Results will appear here...</pre>
    </div>
    
    <h2>Subcategories</h2>
    <div>
        <label for="category-id">Category ID:</label>
        <input type="number" id="category-id" value="1" min="1">
        <button id="test-subcategories">Test Get Subcategories</button>
    </div>
    <div id="subcategories-result" class="result">
        <pre>Results will appear here...</pre>
    </div>
    
    <script>
        document.getElementById('test-categories').addEventListener('click', async () => {
            try {
                const response = await fetch('ajax/get_categories.php');
                const data = await response.json();
                document.querySelector('#categories-result pre').textContent = 
                    JSON.stringify(data, null, 2);
            } catch (error) {
                document.querySelector('#categories-result pre').textContent = 
                    `Error: ${error.message}`;
            }
        });
        
        document.getElementById('test-subcategories').addEventListener('click', async () => {
            try {
                const categoryId = document.getElementById('category-id').value;
                const response = await fetch(`ajax/get_subcategories.php?category_id=${categoryId}`);
                const data = await response.json();
                document.querySelector('#subcategories-result pre').textContent = 
                    JSON.stringify(data, null, 2);
            } catch (error) {
                document.querySelector('#subcategories-result pre').textContent = 
                    `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
