<?php
// /ajax/view_invoice.php
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';
header('Content-Type: application/json');

// Ensure user is authenticated
if (!isset($_SESSION['user'])) {
  echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
  exit;
}

$id = intval($_GET['id'] ?? 0);

if ($id <= 0) {
  echo json_encode(['success' => false, 'message' => 'Invalid invoice ID.']);
  exit;
}

try {
  $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = ?");
  $stmt->execute([$id]);
  $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
  
  if (!$invoice) {
    echo json_encode(['success' => false, 'message' => 'Invoice not found.']);
    exit;
  }
  
  // Calculate tax amount and total
  $taxAmount = ($invoice['amount'] * $invoice['tax']) / 100;
  $total = $invoice['amount'] + $taxAmount;
  
  $invoice['tax_amount'] = $taxAmount;
  $invoice['total'] = $total;
  
  echo json_encode([
    'success' => true,
    'invoice' => $invoice
  ]);
  
} catch (Exception $e) {
  echo json_encode([
    'success' => false,
    'message' => 'Error retrieving invoice: ' . $e->getMessage()
  ]);
}
?>
