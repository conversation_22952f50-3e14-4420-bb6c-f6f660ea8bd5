<?php
// check_categories.php
require_once __DIR__ . '/config/db.php';

echo "<h1>Database Check</h1>";

// Check categories
echo "<h2>Categories</h2>";
try {
    $categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll();
    if (empty($categories)) {
        echo "<p>No categories found in the database.</p>";
    } else {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Name</th><th>Description</th></tr>";
        foreach ($categories as $cat) {
            echo "<tr>";
            echo "<td>" . $cat['id'] . "</td>";
            echo "<td>" . htmlspecialchars($cat['name']) . "</td>";
            echo "<td>" . htmlspecialchars($cat['description']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p>Error querying categories: " . $e->getMessage() . "</p>";
}

// Check subcategories
echo "<h2>Subcategories</h2>";
try {
    $subcategories = $pdo->query("SELECT s.*, c.name as category_name FROM subcategories s JOIN categories c ON s.category_id = c.id ORDER BY s.name")->fetchAll();
    if (empty($subcategories)) {
        echo "<p>No subcategories found in the database.</p>";
    } else {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Category ID</th><th>Category Name</th><th>Name</th><th>Description</th></tr>";
        foreach ($subcategories as $sub) {
            echo "<tr>";
            echo "<td>" . $sub['id'] . "</td>";
            echo "<td>" . $sub['category_id'] . "</td>";
            echo "<td>" . htmlspecialchars($sub['category_name']) . "</td>";
            echo "<td>" . htmlspecialchars($sub['name']) . "</td>";
            echo "<td>" . htmlspecialchars($sub['description']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p>Error querying subcategories: " . $e->getMessage() . "</p>";
}

// Check AJAX response for get_categories.php
echo "<h2>AJAX Response for get_categories.php</h2>";
try {
    $categories = $pdo->query("SELECT id, name FROM categories ORDER BY name")->fetchAll();
    echo "<pre>";
    echo json_encode($categories, JSON_PRETTY_PRINT);
    echo "</pre>";
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

// Check AJAX response for get_subcategories.php
echo "<h2>AJAX Response for get_subcategories.php (for first category)</h2>";
try {
    $firstCategory = $pdo->query("SELECT id FROM categories ORDER BY id LIMIT 1")->fetch();
    if ($firstCategory) {
        $catId = $firstCategory['id'];
        $stmt = $pdo->prepare("SELECT id, name, description FROM subcategories WHERE category_id = ? ORDER BY name");
        $stmt->execute([$catId]);
        $subs = $stmt->fetchAll();
        $response = [
            'success' => true,
            'subcategories' => $subs
        ];
        echo "<p>Category ID: " . $catId . "</p>";
        echo "<pre>";
        echo json_encode($response, JSON_PRETTY_PRINT);
        echo "</pre>";
    } else {
        echo "<p>No categories found to test subcategories.</p>";
    }
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
