<?php
require_once __DIR__ . '/config/db.php';
require_once __DIR__ . '/vendor/autoload.php'; // TCPDF should be installed via Composer

use TCPDF;

$stmt = $pdo->query("SELECT * FROM invoices ORDER BY created_at DESC");
$invoices = $stmt->fetchAll();

$pdf = new TCPDF();
$pdf->AddPage();
$pdf->SetFont('helvetica', '', 12);

$html = '<h2>Invoice Report</h2><table border="1" cellpadding="5">
<tr><th>ID</th><th>Month</th><th>Student Type</th><th>Total Amount</th><th>Created At</th></tr>';

foreach ($invoices as $inv) {
    $html .= '<tr>
        <td>' . $inv['id'] . '</td>
        <td>' . $inv['month_year'] . '</td>
        <td>' . $inv['student_type'] . '</td>
        <td>R ' . number_format($inv['total_amount'], 2) . '</td>
        <td>' . $inv['created_at'] . '</td>
    </tr>';
}

$html .= '</table>';
$pdf->writeHTML($html, true, false, true, false, '');
$pdf->Output('invoices.pdf', 'I');
