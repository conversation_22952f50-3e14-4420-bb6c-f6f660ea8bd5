<?php
// /ajax/update_cost.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

// Debug: Log all POST data
error_log("Update cost POST data: " . print_r($_POST, true));

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = intval($_POST['id'] ?? 0);
    $description = trim($_POST['description'] ?? '');
    $amount = floatval($_POST['amount'] ?? 0);
    $rate_type = $_POST['rate_type'] ?? 'daily';
    $num_days = intval($_POST['num_days'] ?? 1);

    // Debug: Log parsed values
    error_log("Parsed values - ID: $id, Description: $description, Amount: $amount, Rate Type: $rate_type, Days: $num_days");

    // Validate input
    $errors = [];
    if ($id <= 0) {
        $errors[] = "Invalid cost ID";
    }
    if (empty($description)) {
        $errors[] = "Description is required";
    }
    if ($amount <= 0) {
        $errors[] = "Amount must be greater than zero";
    }
    if (!in_array($rate_type, ['daily', 'monthly'])) {
        $errors[] = "Invalid rate type";
    }
    if ($num_days <= 0) {
        $errors[] = "Number of days must be greater than zero";
    }

    if (empty($errors)) {
        try {
            // First, check if the cost exists
            $checkStmt = $pdo->prepare("SELECT id FROM costs WHERE id = ?");
            $checkStmt->execute([$id]);
            $cost = $checkStmt->fetch();

            if ($cost) {
                // Cost exists, proceed with update
                $stmt = $pdo->prepare("UPDATE costs SET description = ?, amount = ?, rate_type = ?, num_days = ? WHERE id = ?");
                $stmt->execute([$description, $amount, $rate_type, $num_days, $id]);

                // Even if no actual changes were made, consider it a success if the cost exists
                echo json_encode(['success' => true, 'message' => 'Cost updated successfully.']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Cost not found.']);
            }
        } catch (Exception $e) {
            error_log("Error updating cost: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Error updating cost: ' . $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid input: ' . implode(', ', $errors)]);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?>