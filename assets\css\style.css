/* ===== VARIABLES & RESET ===== */
:root {
  /* Professional White Theme */
  --primary: #2563eb;        /* Vibrant blue */
  --primary-light: #3b82f6;  /* Lighter blue */
  --primary-dark: #1d4ed8;   /* Darker blue */
  --accent: #7c3aed;         /* Softer purple accent */
  --success: #10b981;        /* Emerald green */
  --warning: #ef4444;        /* Bright red */
  --text: #1f2937;           /* Dark gray */
  --text-secondary: #6b7280; /* Medium gray */
  --bg: #ffffff;             /* Pure white background */
  --card: #ffffff;           /* Card background */
  --border: #e5e7eb;         /* Light gray border */
  --shadow: rgba(0, 0, 0, 0.05);
  --glass: rgba(255, 255, 255, 0.85);

  /* Professional color palette */
  --electric-blue: #0096c7;
  --deep-purple: #6d28d9;
  --soft-pink: #ec4899;

  /* Shared Variables */
  --border-radius: 8px;       /* Slightly rounded */
  --border-radius-lg: 12px;
  --border-radius-sm: 4px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s ease-out;

  /* Subtle shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.12);

  /* Glass morphism */
  --glass-bg: rgba(255, 255, 255, 0.9);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

/* Reset with modern enhancements */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== BASE STYLES ===== */
html {
  font-size: 16px;
  scroll-behavior: smooth;
  scroll-padding-top: 80px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: var(--bg);
  color: var(--text);
  line-height: 1.6;
  transition: var(--transition);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* Professional typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1.25rem;
  font-weight: 600;
  line-height: 1.25;
  color: var(--text);
  letter-spacing: -0.02em;
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
  position: relative;
  padding-bottom: 0.5rem;
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--primary-light));
  border-radius: 2px;
  transition: width 0.4s ease;
}

h1:hover::after {
  width: 100px;
}

h2 {
  font-size: 2rem;
  position: relative;
  padding-bottom: 0.5rem;
}

h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--primary);
  border-radius: 2px;
}

h3 {
  font-size: 1.5rem;
  color: var(--primary);
}

h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1.25rem;
  color: var(--text-secondary);
  line-height: 1.7;
  font-weight: 400;
}

/* Professional links with subtle animation */
a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition-fast);
  font-weight: 500;
  position: relative;
}

a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--primary);
  transition: width 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
}

a:hover::after {
  width: 100%;
}

/* Add Google Font - Inter with professional weights */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* ===== LAYOUT & GRID ===== */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

main {
  flex: 1;
  padding: 2rem 0;
}

.tab {
  display: none;
  animation: fadeInUp 0.4s cubic-bezier(0.22, 1, 0.36, 1) forwards;
}

.tab:first-of-type {
  display: block;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* ===== HEADER & NAVIGATION ===== */
header {
  background: var(--glass-bg);
  color: var(--text);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

header.scrolled {
  box-shadow: var(--shadow-md);
  padding: 0.5rem 0;
}

/* Professional navigation */
.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  transition: var(--transition);
}

.nav-brand {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--text);
  letter-spacing: -0.01em;
}

.nav-brand i {
  margin-right: 0.5rem;
  color: var(--primary);
  font-size: 1.25rem;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.nav-menu a {
  color: var(--text);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition-fast);
  font-weight: 500;
  text-decoration: none;
}

.nav-menu a:hover, .nav-menu a.active {
  color: var(--primary);
  background-color: rgba(37, 99, 235, 0.1);
}

/* Navigation Dropdown Styles */
.nav-dropdown {
  position: relative;
  display: inline-block;
}

.nav-link-with-dropdown {
  color: var(--text);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition-fast);
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
}

.nav-link-with-dropdown:hover, .nav-link-with-dropdown.active {
  color: var(--primary);
  background-color: rgba(37, 99, 235, 0.1);
}

.dropdown-arrow {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.nav-dropdown.open .dropdown-arrow {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  min-width: 280px;
  max-width: 350px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  max-height: 400px;
  overflow-y: auto;
}

.nav-dropdown.open .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-header {
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.dropdown-category {
  border-bottom: 1px solid var(--border);
}

.dropdown-category:last-child {
  border-bottom: none;
}

.dropdown-category-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  color: var(--text);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-fast);
  background: rgba(37, 99, 235, 0.02);
}

.dropdown-category-link:hover {
  background: rgba(37, 99, 235, 0.1);
  color: var(--primary);
}

.dropdown-subcategories {
  background: var(--bg);
}

.dropdown-subcategory-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem 0.5rem 2rem;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 0.9rem;
  transition: var(--transition-fast);
  border-left: 3px solid transparent;
}

.dropdown-subcategory-link:hover {
  background: rgba(37, 99, 235, 0.05);
  color: var(--primary);
  border-left-color: var(--primary);
}

.dropdown-empty {
  padding: 1rem;
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.clear-filters-link {
  background: rgba(239, 68, 68, 0.05) !important;
  color: var(--warning) !important;
  border-bottom: 2px solid rgba(239, 68, 68, 0.3);
  font-weight: 600;
}

.clear-filters-link:hover {
  background: rgba(239, 68, 68, 0.1) !important;
  color: var(--warning) !important;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-initial {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary), var(--primary-light));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition);
}

.user-initial:hover {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.2);
}

/* Special styling for logout link */
.logout-link {
  background-color: rgba(239, 68, 68, 0.1) !important;
  color: var(--warning) !important;
}

.logout-link:hover {
  background-color: rgba(239, 68, 68, 0.2) !important;
}

/* ===== FORMS & INPUTS ===== */
form {
  background-color: var(--card);
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  margin-bottom: 2rem;
  border: 1px solid var(--border);
  display: grid;
  gap: 1.25rem;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  transition: var(--transition);
}

form:hover {
  box-shadow: var(--shadow-md);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  position: relative;
}

.form-label {
  font-weight: 500;
  color: var(--text);
  font-size: 0.9rem;
}

input, select, textarea {
  font-family: inherit;
  font-size: 0.95rem;
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--border);
  background-color: var(--card);
  color: var(--text);
  transition: var(--transition-fast);
  width: 100%;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

input::placeholder, select::placeholder, textarea::placeholder {
  color: var(--text-secondary);
  opacity: 0.6;
}

textarea {
  resize: vertical;
  min-height: 120px;
}

/* Professional buttons */
button, .btn {
  font-family: inherit;
  font-size: 0.95rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  background-color: var(--primary);
  color: white;
  border: none;
  cursor: pointer;
  font-weight: 500;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: var(--transition);
  box-shadow: 0 2px 6px rgba(37, 99, 235, 0.2);
}

button:hover, .btn:hover {
  background-color: var(--secondary);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary:hover, .btn-sm:hover {
  background-color: #1e3a8a; /* Navy blue */
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

button:active, .btn:active {
  transform: translateY(0);
}

.btn-warning {
  background-color: var(--warning);
  box-shadow: 0 2px 6px rgba(239, 68, 68, 0.2);
}

.btn-warning:hover {
  background-color: #dc2626;
}

.btn-success {
  background-color: var(--success);
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.2);
}

.btn-success:hover {
  background-color: #0d9488;
}

.btn-secondary {
  background-color: var(--text-secondary);
  box-shadow: 0 2px 6px rgba(107, 114, 128, 0.2);
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1.05rem;
}

/* Button with icon */
.btn-icon {
  width: 2.25rem;
  height: 2.25rem;
  padding: 0;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* ===== TOAST NOTIFICATIONS ===== */
#toast-container {
  position: fixed;
  top: 30px;
  right: 30px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 350px;
}

.toast {
  display: flex;
  align-items: center;
  padding: 14px 18px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  background-color: var(--card);
  color: var(--text);
  margin-bottom: 10px;
  transform: translateX(150%) scale(0.9);
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  overflow: hidden;
  max-width: 100%;
  border-left: 4px solid transparent;
  opacity: 0;
}

.toast.show {
  transform: translateX(0) scale(1);
  opacity: 1;
}

.toast.hide {
  transform: translateX(150%) scale(0.9);
  opacity: 0;
}

.toast-icon {
  margin-right: 12px;
  font-size: 20px;
  flex-shrink: 0;
}

.toast-content {
  flex-grow: 1;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 500;
}

.toast-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--text-secondary);
  margin-left: 10px;
  padding: 0;
  line-height: 1;
  transition: var(--transition-fast);
}

.toast-close:hover {
  color: var(--text);
}

/* Toast types */
.toast-success {
  border-left-color: var(--success);
}

.toast-success .toast-icon {
  color: var(--success);
}

.toast-error {
  border-left-color: var(--warning);
}

.toast-error .toast-icon {
  color: var(--warning);
}

.toast-warning {
  border-left-color: #f59e0b;
}

.toast-warning .toast-icon {
  color: #f59e0b;
}

.toast-info {
  border-left-color: var(--primary);
}

.toast-info .toast-icon {
  color: var(--primary);
}

/* ===== TABLES & DATA DISPLAY ===== */
table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 2rem;
  background-color: var(--card);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

table:hover {
  box-shadow: var(--shadow-md);
}

th, td {
  padding: 1rem 1.25rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
  transition: var(--transition-fast);
}

th {
  background-color: var(--primary);
  color: white;
  font-weight: 500;
  font-size: 0.85rem;
  position: sticky;
  top: 0;
}

tr:last-child td {
  border-bottom: none;
}

tr:hover td {
  background-color: rgba(59, 130, 246, 0.05);
}

.table-search {
  margin-bottom: 1.5rem;
  max-width: 300px;
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  border: 1px solid var(--border);
  border-radius: var(--border-radius);
  background: var(--card);
  color: var(--text);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-fast);
}

.table-search:focus {
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* ===== CARDS & DASHBOARD ELEMENTS ===== */
.card {
  background-color: var(--card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  padding: 1.5rem;
  border: 1px solid var(--border);
  transition: var(--transition);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.card-header {
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  background: rgba(37, 99, 235, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  transition: var(--transition);
}

.card:hover .card-icon {
  transform: scale(1.1);
  background: var(--primary);
  color: white;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text);
}

.card-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: var(--primary);
  line-height: 1.2;
}

.card-subtitle {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin-bottom: 0;
}

.card-footer {
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.card-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-weight: 500;
}

.card-trend.positive {
  color: var(--success);
}

.card-trend.negative {
  color: var(--warning);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* ===== MODALS & POPUPS ===== */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 20px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.modal.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--card);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  position: relative;
  transform: translateY(30px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border);
}

.modal.active .modal-content {
  transform: translateY(0);
}

.close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem;
  cursor: pointer;
  background: none;
  border: none;
  color: var(--text-secondary);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-fast);
}

.close-modal:hover {
  color: var(--warning);
  background-color: rgba(239, 68, 68, 0.1);
}

/* ===== ANIMATIONS & TRANSITIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== SPECIFIC COMPONENT STYLES ===== */
/* Section titles */
.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text);
  margin: 2rem 0 1.5rem;
  padding-bottom: 0.75rem;
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--primary);
  border-radius: 2px;
  transition: width 0.4s ease;
}

.section-title:hover::after {
  width: 80px;
}

.section-title i {
  color: var(--primary);
  font-size: 1.75rem;
}

.section-subtitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text);
  margin: 1.5rem 0 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-subtitle i {
  color: var(--primary);
  font-size: 1.25rem;
}

/* Category blocks */
.category-block, .category-section {
  background-color: var(--card);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border);
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
}

.category-block:hover, .category-section:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

/* Form row layout for subcategory forms */
.form-row {
  display: grid;
  grid-template-columns: 2fr 3fr auto;
  gap: 1rem;
  align-items: end;
  margin-bottom: 1rem;
}

.subcategory-add-section {
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin: 1rem 0;
  border-left: 3px solid var(--primary);
}

.subcategory-add-section h4 {
  margin: 0 0 1rem 0;
  color: var(--primary);
  font-size: 1rem;
}

.subcategories-section {
  margin-top: 1.5rem;
}

.subcategories-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text);
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: var(--text-secondary);
  background-color: rgba(107, 114, 128, 0.05);
  border-radius: var(--border-radius);
  border: 1px dashed var(--border);
}

.empty-state i {
  margin-right: 0.5rem;
}

.table-container {
  overflow-x: auto;
  border-radius: var(--border-radius);
  border: 1px solid var(--border);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  background-color: var(--card);
}

.data-table th {
  background-color: var(--primary);
  color: white;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 500;
  font-size: 0.9rem;
}

.data-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border);
  vertical-align: middle;
}

.data-table tr:last-child td {
  border-bottom: none;
}

.data-table tr:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

.subcategory-list {
  margin: 1rem 0;
  padding-left: 0;
  list-style: none;
}

.subcategory-list li {
  padding: 0.75rem 1rem;
  border-left: 3px solid var(--primary);
  background-color: rgba(59, 130, 246, 0.05);
  margin-bottom: 0.5rem;
  border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) 0;
  transition: var(--transition-fast);
}

.subcategory-list li:hover {
  transform: translateX(5px);
  background-color: rgba(59, 130, 246, 0.1);
}

.delete-category-btn, .delete-subcategory-btn {
  background: none;
  border: none;
  color: var(--warning);
  cursor: pointer;
  padding: 0.25rem;
  font-size: 0.9rem;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-fast);
  margin-left: 0.25rem;
}

.delete-category-btn:hover, .delete-subcategory-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

/* Category Costs Sections */
.category-costs-section {
  margin-bottom: 2rem;
  border-left: 4px solid var(--primary);
}

.category-costs-section .card-header {
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
}

.category-costs-section .card-title {
  margin: 0;
  color: white;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-summary {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-stats {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 500;
}

.subcategory-costs-section {
  margin: 1.5rem 0;
  padding: 0 1.5rem;
}

.subcategory-header {
  margin-bottom: 1rem;
}

.category-name-in-subcategory {
  color: black;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: linear-gradient(135deg, var(--primary), var(--secondary));
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.subcategory-title {
  color: var(--text);
  font-size: 1rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary);
  margin-left: 1rem;
}

.subcategory-count {
  color: var(--text-secondary);
  font-weight: normal;
  font-size: 0.9rem;
}

.subcategory-table {
  margin-bottom: 1rem;
}

.subcategory-total {
  background-color: rgba(59, 130, 246, 0.1);
  font-weight: 600;
}

.subcategory-total td {
  border-top: 2px solid var(--primary);
  padding: 0.75rem 1rem;
}

.rate-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.rate-daily {
  background-color: rgba(34, 197, 94, 0.1);
  color: var(--success);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.rate-monthly {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--primary);
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Learner Info and Checkbox Styles */
.learner-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.learner-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-group {
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: var(--border-radius);
  padding: 1rem;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  cursor: pointer;
  margin: 0;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--primary);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background-color: var(--primary);
  border-color: var(--primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: "✓";
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.checkbox-text {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.checkbox-text small {
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.form-help {
  color: var(--text-secondary);
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
}

/* Cost Preview Styles */
.cost-preview {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(59, 130, 246, 0.1));
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin: 1rem 0;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.preview-content h4 {
  margin: 0 0 1rem 0;
  color: var(--success);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-breakdown {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.preview-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
}

.preview-total {
  border-top: 2px solid var(--success);
  padding-top: 0.75rem;
  margin-top: 0.5rem;
  font-size: 1.1rem;
}

.preview-total span {
  color: var(--success);
}

/* Responsive form row */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .form-row .form-group:last-child {
    justify-self: stretch;
  }

  .category-costs-section .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .subcategory-costs-section {
    padding: 0 1rem;
  }
}

/* Invoice preview */
#invoice-preview {
  background-color: white;
  color: black;
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  margin-top: 2rem;
  transition: var(--transition);
}

#invoice-preview:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

/* Analytics charts */
canvas {
  background-color: var(--card);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-bottom: 2rem;
  max-width: 100%;
  height: auto !important;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

canvas:hover {
  box-shadow: var(--shadow-md);
}

.chart-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.chart-container {
  background-color: var(--card);
  border-radius: var(--border-radius-lg);
  padding: 1rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition);
}

.chart-container:hover {
  box-shadow: var(--shadow-md);
}

/* Footer */
footer {
  background-color: var(--glass-bg);
  padding: 1.5rem 0;
  text-align: center;
  margin-top: 3rem;
  border-top: 1px solid var(--glass-border);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .container {
    padding: 0 1.5rem;
  }

  .grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }

  .chart-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  html {
    font-size: 15px;
  }

  .top-nav {
    padding: 0.75rem 1.5rem;
  }

  .nav-menu {
    gap: 0.5rem;
  }

  .nav-menu a {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .nav-link-with-dropdown {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .dropdown-menu {
    min-width: 250px;
    max-width: 300px;
  }

  form {
    grid-template-columns: 1fr;
    padding: 1.25rem;
  }

  .modal-content {
    padding: 1.5rem;
  }

  .section-title {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }

  .top-nav {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }

  .nav-menu {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .nav-dropdown {
    width: 100%;
  }

  .nav-link-with-dropdown {
    width: 100%;
    justify-content: center;
  }

  .dropdown-menu {
    position: fixed;
    top: auto;
    left: 5%;
    right: 5%;
    width: 90%;
    max-width: none;
    min-width: auto;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .card {
    padding: 1.25rem;
  }

  #toast-container {
    max-width: 90%;
    left: 5%;
    right: 5%;
    top: 20px;
  }
}

/* === Accessibility Focus Styles === */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: var(--border-radius);
}

/* === Print Styling for Invoice === */
@media print {
  body, html {
    background: white;
    color: black;
  }

  header, nav, footer, .btn, .top-nav, .nav-menu, .toast, .modal, .no-print {
    display: none !important;
  }

  main, #invoice-preview {
    padding: 0;
    margin: 0;
    box-shadow: none !important;
    background: white !important;
  }

  #invoice-preview {
    border: none;
    page-break-after: always;
  }
}