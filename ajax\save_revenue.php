<?php
// /ajax/save_revenue.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $month_year = $_POST['month_year'] ?? '';
    $student_type = $_POST['student_type'] ?? '';
    $revenue_per_student = floatval($_POST['revenue_per_student'] ?? 0);
    $total_students = intval($_POST['total_students'] ?? 0);

    if ($month_year && $student_type && $revenue_per_student > 0 && $total_students > 0) {
        $stmt = $pdo->prepare("INSERT INTO revenue (month_year, student_type, revenue_per_student, total_students) VALUES (?, ?, ?, ?)");
        $stmt->execute([$month_year, $student_type, $revenue_per_student, $total_students]);
        echo json_encode(['success' => true, 'message' => 'Revenue recorded successfully.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid revenue data.']);
    }
    exit();
}
?>