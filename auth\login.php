<?php
// /auth/login.php
require_once __DIR__ . '/../config/db.php';
// Start session if one doesn't already exist
if (session_status() === PHP_SESSION_NONE) {
  session_start();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $username = trim($_POST['username'] ?? '');
  $password = $_POST['password'] ?? '';

  if ($username && $password) {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? LIMIT 1");
    $stmt->execute([$username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user && password_verify($password, $user['password'])) {
      $_SESSION['user'] = $user;
      if ($user['role'] === 'admin') {
        header("Location: ../admin/dashboard.php");
        exit;
      } elseif ($user['role'] === 'viewer') {
        header("Location: ../viewer/viewer_dashboard.php");
        exit;
      } else {
        $error = "Invalid role configuration.";
      }
    } else {
      $error = "Invalid username or password.";
    }
  } else {
    $error = "All fields are required.";
  }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - Learnership Cost & Invoice Management</title>
  <link rel="stylesheet" href="../assets/css/style.css">
  <style>
    body {
      background-color: #f0f2f5;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .auth-container {
      background-color: #ffffff;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 30px;
      width: 100%;
      max-width: 400px;
      text-align: center;
    }

    .auth-header {
      margin-bottom: 30px;
    }

    .auth-header h1 {
      color: #4361ee;
      font-size: 28px;
      margin-bottom: 10px;
    }

    .auth-header p {
      color: #666;
      font-size: 16px;
      margin-bottom: 0;
    }

    .auth-form {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .auth-form input,
    .auth-form button {
      padding: 12px 15px;
      border-radius: 5px;
      border: 1px solid #ddd;
      font-size: 16px;
    }

    .auth-form button {
      background-color: #4361ee;
      color: white;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .auth-form button:hover {
      background-color: #3a0ca3;
    }

    .auth-footer {
      margin-top: 20px;
      font-size: 14px;
      color: #666;
    }

    .auth-footer a {
      color: #4361ee;
      text-decoration: none;
      font-weight: 600;
    }

    .auth-footer a:hover {
      text-decoration: underline;
    }

    .message {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 20px;
    }

    .error-message {
      background-color: #ffebee;
      color: #c62828;
      border-left: 4px solid #c62828;
    }

    .success-message {
      background-color: #e8f5e9;
      color: #2e7d32;
      border-left: 4px solid #2e7d32;
    }
  </style>
</head>
<body>
  <div class="auth-container">
    <div class="auth-header">
      <h1>Welcome Back</h1>
      <p>Login to access your dashboard</p>
    </div>

    <?php if (!empty($error)): ?>
      <div class="message error-message">
        <?php echo $error; ?>
      </div>
    <?php endif; ?>

    <?php if (!empty($_GET['registered'])): ?>
      <div class="message success-message">
        Registration successful. Please login.
      </div>
    <?php endif; ?>

    <form method="POST" class="auth-form">
      <input type="text" name="username" placeholder="Username" required>
      <input type="password" name="password" placeholder="Password" required>
      <button type="submit">Login</button>
    </form>

    <div class="auth-footer">
      <p>Don't have an account? <a href="register.php">Register</a></p>
    </div>
  </div>
</body>
</html>
