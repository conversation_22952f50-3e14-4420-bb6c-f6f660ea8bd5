<?php
// /ajax/view_revenue.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

// Debug: Log request
error_log("View revenue request: " . print_r($_GET, true));

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $id = intval($_GET['id'] ?? 0);

    if ($id > 0) {
        try {
            $stmt = $pdo->prepare("
                SELECT * FROM revenue
                WHERE id = ?
            ");
            $stmt->execute([$id]);
            $revenue = $stmt->fetch(PDO::FETCH_ASSOC); // Ensure we get an associative array

            if ($revenue) {
                // Debug: Log the revenue data
                error_log("Revenue data found: " . print_r($revenue, true));

                // Convert numeric values to appropriate types
                $revenue['id'] = intval($revenue['id']);
                $revenue['revenue_per_student'] = floatval($revenue['revenue_per_student']);
                $revenue['total_students'] = intval($revenue['total_students']);

                echo json_encode([
                    'success' => true,
                    'revenue' => $revenue
                ]);
            } else {
                error_log("Revenue not found for ID: $id");
                echo json_encode([
                    'success' => false,
                    'message' => 'Revenue not found'
                ]);
            }
        } catch (Exception $e) {
            error_log("Error fetching revenue: " . $e->getMessage());
            echo json_encode([
                'success' => false,
                'message' => 'Error fetching revenue: ' . $e->getMessage()
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid revenue ID'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method'
    ]);
}
?>
