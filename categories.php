<?php
// /categories.php
require_once __DIR__ . '/includes/header.php';

// Fetch all categories with their subcategories
try {
    $categories = $pdo->query("SELECT * FROM categories ORDER BY name")->fetchAll();

    // Fetch subcategories for each category
    $categoriesWithSubs = [];
    foreach ($categories as $category) {
        $subcategories = $pdo->prepare("SELECT * FROM subcategories WHERE category_id = ? ORDER BY name");
        $subcategories->execute([$category['id']]);
        $category['subcategories'] = $subcategories->fetchAll();
        $categoriesWithSubs[] = $category;
    }
    $categories = $categoriesWithSubs;
} catch (Exception $e) {
    $categories = [];
    error_log("Error fetching categories: " . $e->getMessage());
}
?>

  <main class="container">
    <h1 class="section-title"><i class="fas fa-tags"></i> Category Management</h1>

    <!-- Add New Category Section -->
    <div class="card">
      <div class="card-header">
        <h2 class="card-title"><i class="fas fa-plus-circle"></i> Add New Category</h2>
      </div>
      <form id="category-form">
        <div class="form-group">
          <label class="form-label" for="category-name">Category Name</label>
          <input type="text" id="category-name" name="name" placeholder="Enter category name" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="category-description">Description</label>
          <textarea id="category-description" name="description" placeholder="Enter category description"></textarea>
        </div>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-plus"></i> Add Category
        </button>
      </form>
    </div>

    <!-- Categories and Subcategories Section -->
    <h2 class="section-subtitle"><i class="fas fa-list"></i> Categories & Subcategories</h2>

    <?php if (empty($categories)): ?>
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">No Categories Found</h3>
        </div>
        <p class="card-subtitle">Add your first category using the form above.</p>
      </div>
    <?php else: ?>
      <?php foreach ($categories as $cat): ?>
        <div class="card category-section" data-category-id="<?= $cat['id'] ?>">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-tag"></i> <?= htmlspecialchars($cat['name']) ?>
            </h3>
            <div class="card-actions">
              <button class="btn btn-sm btn-warning delete-category-btn" data-id="<?= $cat['id'] ?>" title="Delete Category">
                <i class="fas fa-trash"></i> Delete Category
              </button>
            </div>
          </div>

          <?php if (!empty($cat['description'])): ?>
            <p class="card-subtitle"><?= nl2br(htmlspecialchars($cat['description'])) ?></p>
          <?php endif; ?>

          <!-- Add Subcategory Form -->
          <div class="subcategory-add-section">
            <h4><i class="fas fa-plus"></i> Add Subcategory</h4>
            <form class="subcategory-form" data-category-id="<?= $cat['id'] ?>">
              <div class="form-row">
                <div class="form-group">
                  <label class="form-label" for="sub-name-<?= $cat['id'] ?>">Subcategory Name</label>
                  <input type="text" id="sub-name-<?= $cat['id'] ?>" name="name" placeholder="Enter subcategory name" required>
                </div>
                <div class="form-group">
                  <label class="form-label" for="sub-desc-<?= $cat['id'] ?>">Description</label>
                  <input type="text" id="sub-desc-<?= $cat['id'] ?>" name="description" placeholder="Enter description">
                </div>
                <div class="form-group">
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add
                  </button>
                </div>
              </div>
            </form>
          </div>

          <!-- Subcategories Table -->
          <div class="subcategories-section">
            <h4><i class="fas fa-layer-group"></i> Subcategories (<?= count($cat['subcategories']) ?>)</h4>

            <?php if (empty($cat['subcategories'])): ?>
              <div class="empty-state">
                <p><i class="fas fa-info-circle"></i> No subcategories yet. Add one using the form above.</p>
              </div>
            <?php else: ?>
              <div class="table-container">
                <table class="data-table">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Description</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="subcategory-table-<?= $cat['id'] ?>">
                    <?php foreach ($cat['subcategories'] as $sub): ?>
                      <tr data-subcategory-id="<?= $sub['id'] ?>">
                        <td><strong><?= htmlspecialchars($sub['name']) ?></strong></td>
                        <td><?= htmlspecialchars($sub['description'] ?? 'No description') ?></td>
                        <td><?= date('M j, Y', strtotime($sub['created_at'])) ?></td>
                        <td>
                          <button class="btn btn-sm btn-danger delete-subcategory-btn"
                                  data-id="<?= $sub['id'] ?>"
                                  data-category-id="<?= $cat['id'] ?>"
                                  title="Delete Subcategory">
                            <i class="fas fa-trash"></i>
                          </button>
                        </td>
                      </tr>
                    <?php endforeach; ?>
                  </tbody>
                </table>
              </div>
            <?php endif; ?>
          </div>
        </div>
      <?php endforeach; ?>
    <?php endif; ?>
  </main>

  <script src="assets/js/categories.js"></script>
<?php require_once __DIR__ . '/includes/footer.php'; ?>
