<?php
// /ajax/update_invoice.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

$id = intval($_POST['id'] ?? 0);
$student_name = trim($_POST['student_name'] ?? '');
$course = trim($_POST['course'] ?? '');
$amount = floatval($_POST['amount'] ?? 0);
$tax = floatval($_POST['tax'] ?? 0);
$due_date = $_POST['due_date'] ?? '';

if ($id && $student_name && $course && $amount > 0 && $due_date) {
  $stmt = $pdo->prepare("UPDATE invoices SET student_name = ?, course = ?, amount = ?, tax = ?, due_date = ? WHERE id = ?");
  $stmt->execute([$student_name, $course, $amount, $tax, $due_date, $id]);
  echo json_encode(['success' => true, 'message' => 'Invoice updated successfully.']);
} else {
  echo json_encode(['success' => false, 'message' => 'Invalid input for invoice update.']);
}
?>