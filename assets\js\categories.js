// /assets/js/categories.js
// Category and Subcategory Management

document.addEventListener('DOMContentLoaded', () => {
  console.log('Categories management initialized');

  // Helper to POST (FormData or JSON) and decode JSON
  async function postJson(url, data) {
    const opts = data instanceof FormData
      ? { method: "POST", body: data }
      : {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(data)
        };

    const res = await fetch(url, opts);
    if (!res.ok) throw new Error(`Network error: ${res.status}`);
    return res.json();
  }

  // Add Category Form
  const categoryForm = document.getElementById('category-form');
  if (categoryForm) {
    categoryForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      console.log('Category form submitted');

      try {
        const formData = new FormData(categoryForm);
        const response = await postJson('./ajax/save_category.php', formData);

        if (response.success) {
          if (window.showToast) {
            window.showToast(response.message || "Category added successfully", "success");
          } else {
            alert(response.message || "Category added successfully");
          }
          categoryForm.reset();
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          if (window.showToast) {
            window.showToast(response.message || "Failed to add category", "error");
          } else {
            alert(response.message || "Failed to add category");
          }
        }
      } catch (error) {
        console.error("Error adding category:", error);
        if (window.showToast) {
          window.showToast("Failed to add category", "error");
        } else {
          alert("Failed to add category");
        }
      }
    });
  }

  // Add Subcategory Forms
  document.querySelectorAll('.subcategory-form').forEach(form => {
    form.addEventListener('submit', async (e) => {
      e.preventDefault();
      const categoryId = form.dataset.categoryId;
      console.log('Subcategory form submitted for category:', categoryId);

      try {
        const formData = new FormData(form);
        formData.append('category_id', categoryId);

        const response = await postJson('./ajax/save_subcategory.php', formData);

        if (response.success) {
          if (window.showToast) {
            window.showToast(response.message || "Subcategory added successfully", "success");
          } else {
            alert(response.message || "Subcategory added successfully");
          }
          form.reset();
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          if (window.showToast) {
            window.showToast(response.message || "Failed to add subcategory", "error");
          } else {
            alert(response.message || "Failed to add subcategory");
          }
        }
      } catch (error) {
        console.error("Error adding subcategory:", error);
        if (window.showToast) {
          window.showToast("Failed to add subcategory", "error");
        } else {
          alert("Failed to add subcategory");
        }
      }
    });
  });

  // Delete Category Buttons
  document.addEventListener('click', async (e) => {
    if (e.target.closest('.delete-category-btn')) {
      e.preventDefault();
      const btn = e.target.closest('.delete-category-btn');
      const categoryId = btn.dataset.id;
      console.log('Delete category clicked for ID:', categoryId);

      if (confirm('Are you sure you want to delete this category? This will also delete all its subcategories.')) {
        try {
          const response = await postJson('./ajax/delete_category.php', { id: categoryId });

          if (response.success) {
            if (window.showToast) {
              window.showToast(response.message || "Category deleted successfully", "success");
            } else {
              alert(response.message || "Category deleted successfully");
            }
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            if (window.showToast) {
              window.showToast(response.message || "Failed to delete category", "error");
            } else {
              alert(response.message || "Failed to delete category");
            }
          }
        } catch (error) {
          console.error("Error deleting category:", error);
          if (window.showToast) {
            window.showToast("Failed to delete category", "error");
          } else {
            alert("Failed to delete category");
          }
        }
      }
    }
  });

  // Delete Subcategory Buttons
  document.addEventListener('click', async (e) => {
    if (e.target.closest('.delete-subcategory-btn')) {
      e.preventDefault();
      const btn = e.target.closest('.delete-subcategory-btn');
      const subcategoryId = btn.dataset.id;
      console.log('Delete subcategory clicked for ID:', subcategoryId);

      if (confirm('Are you sure you want to delete this subcategory?')) {
        try {
          const response = await postJson('./ajax/delete_subcategory.php', { id: subcategoryId });

          if (response.success) {
            if (window.showToast) {
              window.showToast(response.message || "Subcategory deleted successfully", "success");
            } else {
              alert(response.message || "Subcategory deleted successfully");
            }
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            if (window.showToast) {
              window.showToast(response.message || "Failed to delete subcategory", "error");
            } else {
              alert(response.message || "Failed to delete subcategory");
            }
          }
        } catch (error) {
          console.error("Error deleting subcategory:", error);
          if (window.showToast) {
            window.showToast("Failed to delete subcategory", "error");
          } else {
            alert("Failed to delete subcategory");
          }
        }
      }
    }
  });
});
