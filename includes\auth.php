<?php
// /includes/auth.php
// Only start session if one doesn't already exist
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
function is_logged_in() {
    return isset($_SESSION['user']);
}
function authenticate_admin() {
    if (!is_logged_in() || $_SESSION['user']['role'] !== 'admin') {
        header('Location: /auth/login.php');
        exit();
    }
}
function authenticate_viewer() {
    if (!is_logged_in() || $_SESSION['user']['role'] !== 'viewer') {
        header('Location: /auth/login.php');
        exit();
    }
}
?>