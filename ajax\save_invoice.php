<?php
// /ajax/save_invoice.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

$student_name = trim($_POST['student_name'] ?? '');
$course = trim($_POST['course'] ?? '');
$amount = floatval($_POST['amount'] ?? 0);
$tax = floatval($_POST['tax'] ?? 0);
$due_date = $_POST['due_date'] ?? '';

if ($student_name && $course && $amount > 0 && $due_date) {
  $stmt = $pdo->prepare("INSERT INTO invoices (student_name, course, amount, tax, due_date) VALUES (?, ?, ?, ?, ?)");
  $stmt->execute([$student_name, $course, $amount, $tax, $due_date]);
  $invoice_id = $pdo->lastInsertId();

  echo json_encode([
    'success' => true,
    'message' => 'Invoice saved.',
    'invoice_id' => $invoice_id
  ]);
} else {
  echo json_encode(['success' => false, 'message' => 'All required fields must be filled.']);
}
?>