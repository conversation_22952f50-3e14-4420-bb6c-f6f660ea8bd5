<?php
// setup_database.php
// This script will set up the database and tables

// Database connection parameters
$host = 'localhost';
$user = 'root';
$pass = '';
$charset = 'utf8mb4';

try {
    // Connect to MySQL without selecting a database
    $pdo = new PDO("mysql:host=$host;charset=$charset", $user, $pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS cost_calculator");
    echo "Database 'cost_calculator' created or already exists.<br>";
    
    // Select the database
    $pdo->exec("USE cost_calculator");
    
    // Read and execute SQL from the setup file
    $sql = file_get_contents('setup_database.sql');
    $pdo->exec($sql);
    
    echo "Database setup completed successfully!<br>";
    echo "You can now <a href='/admin/categories.php'>manage categories</a> or <a href='/admin/dashboard.php'>go to the dashboard</a>.";
    
} catch (PDOException $e) {
    die("Database setup failed: " . $e->getMessage());
}
?>
