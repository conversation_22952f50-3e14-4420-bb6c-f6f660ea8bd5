<?php
// /ajax/delete_revenue.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

// Debug: Log request
error_log("Delete revenue request: " . print_r($_POST, true));

// Get JSON input for POST requests
$input = json_decode(file_get_contents('php://input'), true);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Try to get ID from either JSON input or POST data
    $id = intval($input['id'] ?? $_POST['id'] ?? 0);

    error_log("Delete revenue ID: $id");

    if ($id > 0) {
        try {
            $stmt = $pdo->prepare("DELETE FROM revenue WHERE id = ?");
            $stmt->execute([$id]);

            if ($stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => 'Revenue deleted successfully.']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Revenue not found or already deleted.']);
            }
        } catch (Exception $e) {
            error_log("Error deleting revenue: " . $e->getMessage());
            echo json_encode(['success' => false, 'message' => 'Error deleting revenue: ' . $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid revenue ID.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?>