// /assets/js/theme.js

document.addEventListener("DOMContentLoaded", () => {
  // ——— THEME TOGGLE SWITCH ———
  function createThemeToggle() {
    const header = document.querySelector("header");
    if (!header) return null;

    const wrapper = document.createElement("div");
    wrapper.className = "theme-switch-wrapper";

    const label = document.createElement("label");
    label.className = "theme-switch";

    const input = document.createElement("input");
    input.type = "checkbox";
    input.id   = "theme-toggle-checkbox";

    const slider = document.createElement("span");
    slider.className = "slider";

    const icons = document.createElement("div");
    icons.className = "slider-icons";

    const moonIcon = document.createElement("span");
    moonIcon.className = "moon";
    moonIcon.textContent = "🌙";

    const sunIcon = document.createElement("span");
    sunIcon.className = "sun";
    sunIcon.textContent = "☀️";

    icons.append(moonIcon, sunIcon);
    slider.append(icons);
    label.append(input, slider);
    wrapper.append(label);
    header.append(wrapper);

    return input;
  }

  let themeToggle = document.getElementById("theme-toggle-checkbox");
  if (!themeToggle) themeToggle = createThemeToggle();

  // Apply saved or system theme
  const saved = localStorage.getItem("theme");
  const darkPreferred = window.matchMedia("(prefers-color-scheme: dark)").matches;
  if (saved === "dark" || (!saved && darkPreferred)) {
    document.body.classList.add("dark-mode");
    if (themeToggle) themeToggle.checked = true;
  }

  if (themeToggle) {
    themeToggle.addEventListener("change", () => {
      const isDark = themeToggle.checked;
      document.body.classList.toggle("dark-mode", isDark);
      localStorage.setItem("theme", isDark ? "dark" : "light");
    });
  }

  // ——— LOGOUT LINKS ———
  document.querySelectorAll(".logout-link").forEach(link => {
    link.addEventListener("click", e => {
      e.preventDefault();
      window.location.href = "/logout.php";
    });
  });

  // ——— MODAL CLOSE BUTTONS ———
  document.querySelectorAll(".close-modal").forEach(btn => {
    btn.addEventListener("click", () => {
      const modal = btn.closest(".modal");
      if (modal) modal.style.display = "none";
    });
  });

  // ——— CLICK-OUTSIDE TO CLOSE MODAL ———
  window.addEventListener("click", e => {
    document.querySelectorAll(".modal").forEach(modal => {
      if (e.target === modal) modal.style.display = "none";
    });
  });

  // (Other theme-related UI handlers can go here…)
});
