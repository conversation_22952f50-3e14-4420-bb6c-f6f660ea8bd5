# Cost Calculator Application

## Setup Instructions

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache, Nginx, etc.)

### Installation Steps

1. **Set up a web server**
   - If you're using XAMPP, place the project in the `htdocs` directory
   - If you're using WAMP, place the project in the `www` directory

2. **Configure the database**
   - Open `config/db.php` and update the database credentials if needed
   - By default, it's configured for:
     - Host: localhost
     - Database: cost_calculator
     - Username: root
     - Password: (empty)

3. **Run the database setup script**
   - Navigate to `http://localhost/cost_calculator_may/setup_database.php` in your browser
   - This will create the database and necessary tables

4. **Login to the application**
   - Navigate to `http://localhost/cost_calculator_may/`
   - Use the default admin credentials:
     - Username: admin
     - Password: admin123

## Using the Application

### Managing Categories
1. As an admin, you can manage categories by going to `http://localhost/cost_calculator_may/admin/categories.php`
2. To add a new category:
   - Enter the category name and description in the form at the top
   - Click "Add Category"
3. To add subcategories:
   - Find the category you want to add a subcategory to
   - Enter the subcategory name and description in the form under that category
   - Click "Add Subcategory"

### Adding Costs
1. Go to the admin dashboard
2. In the "Costs" tab:
   - Fill in the description and amount
   - Select a category and subcategory
   - Choose the rate type (daily or monthly)
   - Enter the number of days
   - Click "Add Cost"

### Adding Revenue
1. Go to the admin dashboard
2. In the "Revenue" tab:
   - Select the month and year
   - Choose the student type
   - Enter the revenue per student and total number of students
   - Click "Add Revenue"

### Generating Invoices
1. Go to the admin dashboard
2. In the "Invoices" tab:
   - Fill in the student name, course, amount, tax, and due date
   - Click "Generate & Save Invoice"

## Troubleshooting

### Cannot Add Categories
If you're having trouble adding categories, check the following:

1. Make sure you're logged in as an admin
2. Verify that the database connection is working
3. Check that the categories table exists in the database
4. Look for any JavaScript errors in the browser console

### Database Connection Issues
If you're experiencing database connection problems:

1. Verify your MySQL server is running
2. Check the credentials in `config/db.php`
3. Make sure the database exists
4. Try running the setup script again: `http://localhost/cost_calculator_may/setup_database.php`
