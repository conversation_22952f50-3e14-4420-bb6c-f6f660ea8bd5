<?php
// System Diagnostic Tool
$title = "System Diagnostic Tool";
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            color: #4361ee;
            border-bottom: 2px solid #4361ee;
            padding-bottom: 10px;
        }
        h2 {
            color: #3a0ca3;
            margin-top: 30px;
        }
        .card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-left: 5px solid #28a745;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left: 5px solid #ffc107;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-left: 5px solid #dc3545;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .btn {
            display: inline-block;
            padding: 10px 15px;
            background-color: #4361ee;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #3a0ca3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><?php echo $title; ?></h1>
        
        <div class="card">
            <h2>System Information</h2>
            <table>
                <tr>
                    <th>Item</th>
                    <th>Value</th>
                </tr>
                <tr>
                    <td>PHP Version</td>
                    <td><?php echo phpversion(); ?></td>
                </tr>
                <tr>
                    <td>Server Software</td>
                    <td><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></td>
                </tr>
                <tr>
                    <td>Operating System</td>
                    <td><?php echo PHP_OS; ?></td>
                </tr>
                <tr>
                    <td>Document Root</td>
                    <td><?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown'; ?></td>
                </tr>
                <tr>
                    <td>Current Script Path</td>
                    <td><?php echo __FILE__; ?></td>
                </tr>
            </table>
        </div>
        
        <div class="card">
            <h2>PHP Extensions</h2>
            <?php
            $requiredExtensions = [
                'pdo_mysql' => 'PDO MySQL',
                'mysqli' => 'MySQLi',
                'json' => 'JSON',
                'session' => 'Session',
                'gd' => 'GD (for image processing)',
                'mbstring' => 'Multibyte String'
            ];
            
            echo '<table>';
            echo '<tr><th>Extension</th><th>Status</th></tr>';
            
            $allAvailable = true;
            foreach ($requiredExtensions as $ext => $name) {
                $available = extension_loaded($ext);
                $class = $available ? 'success' : 'error';
                $status = $available ? 'Available' : 'Not Available';
                
                if (!$available && ($ext == 'pdo_mysql' || $ext == 'mysqli')) {
                    $allAvailable = false;
                }
                
                echo "<tr class='$class'><td>$name ($ext)</td><td>$status</td></tr>";
            }
            echo '</table>';
            
            if (!$allAvailable) {
                echo '<div class="card error">';
                echo '<h3>Database Extensions Missing</h3>';
                echo '<p>Your PHP installation is missing required database extensions. You need at least one of these:</p>';
                echo '<ul>';
                echo '<li>PDO MySQL (pdo_mysql)</li>';
                echo '<li>MySQLi (mysqli)</li>';
                echo '</ul>';
                echo '<p><strong>How to fix:</strong></p>';
                echo '<ol>';
                echo '<li>Find your php.ini file (check the PHP Info section below for its location)</li>';
                echo '<li>Uncomment these lines by removing the semicolon (;) at the beginning:';
                echo '<pre>';
                echo ';extension=mysqli<br>';
                echo ';extension=pdo_mysql';
                echo '</pre></li>';
                echo '<li>Save the file and restart your web server</li>';
                echo '</ol>';
                echo '</div>';
            }
            ?>
        </div>
        
        <div class="card">
            <h2>File System</h2>
            <?php
            $paths = [
                'Root Directory' => '.',
                'Config Directory' => './config',
                'Assets Directory' => './assets',
                'Admin Directory' => './admin',
                'Auth Directory' => './auth',
                'AJAX Directory' => './ajax'
            ];
            
            echo '<table>';
            echo '<tr><th>Path</th><th>Status</th><th>Permissions</th></tr>';
            
            foreach ($paths as $name => $path) {
                $exists = file_exists($path);
                $class = $exists ? 'success' : 'error';
                $status = $exists ? 'Exists' : 'Missing';
                $perms = $exists ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A';
                
                echo "<tr class='$class'><td>$name ($path)</td><td>$status</td><td>$perms</td></tr>";
            }
            echo '</table>';
            ?>
        </div>
        
        <div class="card">
            <h2>Important Files</h2>
            <?php
            $files = [
                'Database Config' => './config/db.php',
                'Setup Script' => './setup.php',
                'Index File' => './index.php',
                'Login Page' => './auth/login.php',
                'Admin Dashboard' => './admin/dashboard.php',
                'CSS Stylesheet' => './assets/css/style.css'
            ];
            
            echo '<table>';
            echo '<tr><th>File</th><th>Status</th><th>Size</th><th>Last Modified</th></tr>';
            
            foreach ($files as $name => $file) {
                $exists = file_exists($file);
                $class = $exists ? 'success' : 'error';
                $status = $exists ? 'Exists' : 'Missing';
                $size = $exists ? filesize($file) . ' bytes' : 'N/A';
                $modified = $exists ? date('Y-m-d H:i:s', filemtime($file)) : 'N/A';
                
                echo "<tr class='$class'><td>$name ($file)</td><td>$status</td><td>$size</td><td>$modified</td></tr>";
            }
            echo '</table>';
            ?>
        </div>
        
        <div class="card">
            <h2>Next Steps</h2>
            <p>Based on the diagnostics above, you can take the following actions:</p>
            
            <a href="phpinfo.php" class="btn">View Full PHP Info</a>
            <a href="setup.php" class="btn">Run Database Setup</a>
            <a href="index.php" class="btn">Go to Homepage</a>
        </div>
    </div>
</body>
</html>
