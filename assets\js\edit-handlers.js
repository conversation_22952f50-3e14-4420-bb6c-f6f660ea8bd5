/**
 * Edit and Delete handlers for dashboard
 * Handles edit and delete operations with toast notifications
 */

document.addEventListener('DOMContentLoaded', () => {
  // Modal handling
  const modals = document.querySelectorAll('.modal');
  const closeButtons = document.querySelectorAll('.close-modal');

  // Close modal when clicking the close button
  closeButtons.forEach(btn => {
    btn.addEventListener('click', () => {
      modals.forEach(modal => {
        modal.style.display = 'none';
      });
    });
  });

  // Close modal when clicking outside the modal content
  window.addEventListener('click', (e) => {
    modals.forEach(modal => {
      if (e.target === modal) {
        modal.style.display = 'none';
      }
    });
  });

  // === COST EDIT & DELETE ===
  // Edit cost button click
  document.querySelectorAll('.edit-btn').forEach(btn => {
    btn.addEventListener('click', async () => {
      const costId = btn.dataset.id;
      try {
        // Fetch cost data
        const response = await fetch(`../ajax/view_cost.php?id=${costId}`);
        const data = await response.json();

        if (data.success) {
          // Populate form
          document.getElementById('edit-cost-id').value = data.cost.id;
          document.getElementById('edit-cost-description').value = data.cost.description;
          document.getElementById('edit-cost-amount').value = data.cost.amount;
          document.getElementById('edit-cost-days').value = data.cost.num_days;
          document.getElementById('edit-cost-rate').value = data.cost.rate_type;

          // Show modal
          document.getElementById('edit-cost-modal').style.display = 'block';
        } else {
          showToast(data.message || "Failed to load cost data", "error");
        }
      } catch (error) {
        console.error("Error fetching cost data:", error);
        showToast("Failed to load cost data", "error");
      }
    });
  });

  // Edit cost form submit
  const editCostForm = document.getElementById('edit-cost-form');
  if (editCostForm) {
    editCostForm.addEventListener('submit', async (e) => {
      e.preventDefault();
      try {
        // Log form data for debugging
        const formData = new FormData(editCostForm);
        console.log("Submitting edit form with data:");
        for (let [key, value] of formData.entries()) {
          console.log(`${key}: ${value}`);
        }

        // Send the form data to the server
        const response = await fetch('../ajax/update_cost.php', {
          method: 'POST',
          body: formData
        });

        // Parse the response
        const text = await response.text();
        console.log("Raw response:", text);

        let data;
        try {
          data = JSON.parse(text);
        } catch (e) {
          console.error("Failed to parse JSON response:", e);
          window.showToast("Server returned invalid response", "error");
          return;
        }

        // Handle the response
        if (data.success) {
          window.showToast(data.message || "Cost updated successfully", "success");
          document.getElementById('edit-cost-modal').style.display = 'none';

          // Refresh the page after a short delay
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          window.showToast(data.message || "Failed to update cost", "error");
        }
      } catch (error) {
        console.error("Error updating cost:", error);
        showToastFallback("Failed to update cost: " + error.message, "error");
      }
    });
  }

  // Delete cost button click
  document.querySelectorAll('.delete-btn').forEach(btn => {
    btn.addEventListener('click', async () => {
      const costId = btn.dataset.id;
      if (confirm("Are you sure you want to delete this cost entry?")) {
        try {
          const response = await fetch('../ajax/delete_cost.php', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: costId })
          });
          const data = await response.json();

          if (data.success) {
            showToastFallback(data.message || "Cost deleted successfully", "success");

            // Refresh the page after a short delay
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            showToastFallback(data.message || "Failed to delete cost", "error");
          }
        } catch (error) {
          console.error("Error deleting cost:", error);
          showToastFallback("Failed to delete cost", "error");
        }
      }
    });
  });

  // Similar handlers for revenue and invoice edit/delete
  // These follow the same pattern as the cost handlers
});
