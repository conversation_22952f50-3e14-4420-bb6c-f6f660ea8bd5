<?php
require_once __DIR__ . '/includes/header.php';
require_once __DIR__ . '/config/db.php';

$stmt = $pdo->query("SELECT * FROM invoices ORDER BY created_at DESC");
$invoices = $stmt->fetchAll();
?>
<main class="container">
  <h1 class="section-title"><i class="fas fa-file-invoice"></i> Invoices</h1>
  <a href="export_invoice_pdf.php" class="btn btn-secondary" style="float:right;margin-bottom:10px;">
    <i class="fas fa-file-pdf"></i> Export to PDF
  </a>
  <table class="table">
    <thead>
      <tr>
        <th>ID</th>
        <th>Month</th>
        <th>Student Type</th>
        <th>Total Amount</th>
        <th>Created At</th>
      </tr>
    </thead>
    <tbody>
      <?php if ($invoices): ?>
        <?php foreach ($invoices as $invoice): ?>
          <tr>
            <td><?= htmlspecialchars($invoice['id']) ?></td>
            <td><?= htmlspecialchars($invoice['month_year']) ?></td>
            <td><?= htmlspecialchars($invoice['student_type']) ?></td>
            <td>R <?= number_format($invoice['total_amount'], 2) ?></td>
            <td><?= htmlspecialchars($invoice['created_at']) ?></td>
          </tr>
        <?php endforeach; ?>
      <?php else: ?>
        <tr><td colspan="5">No invoices found.</td></tr>
      <?php endif; ?>
    </tbody>
  </table>
</main>
