<?php
// ajax/get_subcategories.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

$category_id = intval($_GET['category_id'] ?? 0);

try {
    $stmt = $pdo->prepare("
      SELECT id, name, description
      FROM subcategories
      WHERE category_id = ?
      ORDER BY name
    ");
    $stmt->execute([$category_id]);
    $subs = $stmt->fetchAll();
    echo json_encode([
      'success'       => true,
      'subcategories' => $subs
    ]);
} catch (Exception $e) {
    echo json_encode([
      'success' => false,
      'message' => 'Could not load subcategories: ' . $e->getMessage()
    ]);
}
