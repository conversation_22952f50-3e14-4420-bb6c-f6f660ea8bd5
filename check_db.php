<?php
// Simple script to check database connection
echo "<h1>Database Connection Check</h1>";

$host = 'sql307.infinityfree.com';
$user = 'if0_39163993';
$pass = 'sV5RSK88Jk5';
$db = 'if0_39163993_cost_calculator';

// Try mysqli connection
echo "<h2>Testing MySQLi Connection</h2>";
try {
    $mysqli = new mysqli($host, $user, $pass);
    
    if ($mysqli->connect_error) {
        echo "<p style='color:red;'>MySQLi Connection Failed: " . $mysqli->connect_error . "</p>";
    } else {
        echo "<p style='color:green;'>MySQLi Connection Successful!</p>";
        
        // Check if database exists
        $result = $mysqli->query("SHOW DATABASES LIKE '$db'");
        if ($result->num_rows > 0) {
            echo "<p style='color:green;'>Database '$db' exists.</p>";
            
            // Select the database
            $mysqli->select_db($db);
            
            // Check tables
            $tables = ['users', 'categories', 'subcategories', 'costs', 'revenue', 'invoices'];
            echo "<h3>Checking Tables:</h3>";
            echo "<ul>";
            
            foreach ($tables as $table) {
                $result = $mysqli->query("SHOW TABLES LIKE '$table'");
                if ($result->num_rows > 0) {
                    echo "<li style='color:green;'>Table '$table' exists.</li>";
                } else {
                    echo "<li style='color:red;'>Table '$table' does not exist!</li>";
                }
            }
            
            echo "</ul>";
        } else {
            echo "<p style='color:red;'>Database '$db' does not exist!</p>";
        }
        
        $mysqli->close();
    }
} catch (Exception $e) {
    echo "<p style='color:red;'>MySQLi Error: " . $e->getMessage() . "</p>";
}

// Try PDO connection
echo "<h2>Testing PDO Connection</h2>";
try {
    $dsn = "mysql:host=$host;dbname=$db";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    
    $pdo = new PDO($dsn, $user, $pass, $options);
    echo "<p style='color:green;'>PDO Connection Successful!</p>";
    
    // Check tables
    $tables = ['users', 'categories', 'subcategories', 'costs', 'revenue', 'invoices'];
    echo "<h3>Checking Tables with PDO:</h3>";
    echo "<ul>";
    
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT 1 FROM $table LIMIT 1");
            echo "<li style='color:green;'>Table '$table' exists and is accessible.</li>";
        } catch (PDOException $e) {
            echo "<li style='color:red;'>Table '$table' error: " . $e->getMessage() . "</li>";
        }
    }
    
    echo "</ul>";
} catch (PDOException $e) {
    echo "<p style='color:red;'>PDO Connection Failed: " . $e->getMessage() . "</p>";
}

// Show PHP extensions
echo "<h2>PHP MySQL Extensions</h2>";
echo "<ul>";
if (extension_loaded('mysqli')) {
    echo "<li style='color:green;'>MySQLi extension is loaded.</li>";
} else {
    echo "<li style='color:red;'>MySQLi extension is NOT loaded!</li>";
}

if (extension_loaded('pdo_mysql')) {
    echo "<li style='color:green;'>PDO MySQL extension is loaded.</li>";
} else {
    echo "<li style='color:red;'>PDO MySQL extension is NOT loaded!</li>";
}
echo "</ul>";

// Show next steps
echo "<h2>Next Steps</h2>";
echo "<p>If you see any red text above, there are issues that need to be fixed.</p>";
echo "<p>If all connections are successful but tables don't exist, run the <a href='create_tables.php'>Create Tables Script</a>.</p>";
echo "<p>Once everything is green, try to <a href='auth/login.php'>login</a> with username 'admin' and password 'admin123'.</p>";
?>
