<?php
// update_constraints.php
require_once __DIR__ . '/config/db.php';

try {
    // Drop existing foreign keys on costs table
    $pdo->exec("
        ALTER TABLE costs 
        DROP FOREIGN KEY IF EXISTS costs_ibfk_1,
        DROP FOREIGN KEY IF EXISTS costs_ibfk_2
    ");
    
    // Add foreign keys with ON DELETE CASCADE
    $pdo->exec("
        ALTER TABLE costs
        ADD CONSTRAINT costs_category_fk FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
        ADD CONSTRAINT costs_subcategory_fk FOREIGN KEY (subcategory_id) REFERENCES subcategories(id) ON DELETE CASCADE
    ");
    
    echo "Database constraints updated successfully!";
    
} catch (PDOException $e) {
    die("Error updating constraints: " . $e->getMessage());
}
?>
