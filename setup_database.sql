-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS cost_calculator;
USE cost_calculator;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  role ENUM('admin', 'viewer') NOT NULL DEFAULT 'viewer',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create subcategories table
CREATE TABLE IF NOT EXISTS subcategories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  category_id INT NOT NULL,
  name VARCHA<PERSON>(100) NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  <PERSON>OREIG<PERSON> KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- Create costs table
CREATE TABLE IF NOT EXISTS costs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  description VARCHAR(255) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  category_id INT NOT NULL,
  subcategory_id INT NOT NULL,
  rate_type ENUM('daily', 'monthly') NOT NULL DEFAULT 'daily',
  num_days INT NOT NULL DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE,
  FOREIGN KEY (subcategory_id) REFERENCES subcategories(id) ON DELETE CASCADE
);

-- Create revenue table
CREATE TABLE IF NOT EXISTS revenue (
  id INT AUTO_INCREMENT PRIMARY KEY,
  month_year VARCHAR(7) NOT NULL,
  student_type VARCHAR(50) NOT NULL,
  revenue_per_student DECIMAL(10,2) NOT NULL,
  total_students INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create invoices table
CREATE TABLE IF NOT EXISTS invoices (
  id INT AUTO_INCREMENT PRIMARY KEY,
  student_name VARCHAR(100) NOT NULL,
  course VARCHAR(100) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  tax DECIMAL(5,2) NOT NULL DEFAULT 0,
  due_date DATE NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin user (username: admin, password: admin123)
INSERT IGNORE INTO users (username, password, role) VALUES
('admin', '$2y$10$8WxmVVVlgLBqtGH1mQp9/.Fy9WQKMKo5pN6vQkiA.eVbVZ2g6QmMi', 'admin');

-- Insert some sample categories
INSERT IGNORE INTO categories (id, name, description) VALUES
(1, 'Salaries', 'Staff and instructor salaries'),
(2, 'Facilities', 'Building rent and maintenance'),
(3, 'Equipment', 'Learning equipment and supplies'),
(4, 'Marketing', 'Advertising and promotional activities');

-- Insert some sample subcategories
INSERT IGNORE INTO subcategories (category_id, name, description) VALUES
(1, 'Full-time Staff', 'Permanent employees'),
(1, 'Part-time Instructors', 'Hourly instructors'),
(2, 'Rent', 'Monthly building rent'),
(2, 'Utilities', 'Electricity, water, internet'),
(3, 'Computers', 'Laptops and desktops'),
(3, 'Software', 'Licensed software'),
(4, 'Online Ads', 'Google and social media ads'),
(4, 'Print Materials', 'Brochures and flyers');
