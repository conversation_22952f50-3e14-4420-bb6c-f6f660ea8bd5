<?php
// /includes/header.php
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/auth.php';

// Ensure user is authenticated
if (!isset($_SESSION['user'])) {
  header('Location: ../auth/login.php');
  exit;
}

// Get current page for active link highlighting
$current_page = basename($_SERVER['PHP_SELF']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Learnership Management System</title>
  <?php
  // Determine the correct path to assets based on the current file location
  $current_dir = basename(dirname($_SERVER['PHP_SELF']));
  $base_path = ($current_dir === 'admin' || $current_dir === 'auth' || $current_dir === 'viewer') ? '../' : '';

  // Check user role for navigation
  $user_role = $_SESSION['user']['role'] ?? 'guest';
  ?>
  <link rel="stylesheet" href="<?= $base_path ?>assets/css/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script defer src="<?= $base_path ?>assets/js/toast.js"></script>
  <script defer src="<?= $base_path ?>assets/js/dashboard.js"></script>
</head>
<body>
  <!-- Toast Container -->
  <div id="toast-container"></div>

  <header>
    <div class="top-nav">
      <div class="nav-brand">
        <i class="fas fa-chart-line"></i>
        <span>Learnership Manager</span>
      </div>
      <div class="nav-menu">
        <?php if ($current_dir === 'admin'): ?>
          <!-- Admin Navigation -->
          <a href="costs.php" class="<?= $current_page == 'costs.php' ? 'active' : '' ?>">
            <i class="fas fa-money-bill-wave"></i> Costs
          </a>
          <a href="revenue.php" class="<?= $current_page == 'revenue.php' ? 'active' : '' ?>">
            <i class="fas fa-coins"></i> Revenue
          </a>
          <a href="analytics.php" class="<?= $current_page == 'analytics.php' ? 'active' : '' ?>">
            <i class="fas fa-chart-bar"></i> Analytics
          </a>
          <a href="../categories.php" class="<?= $current_page == 'categories.php' ? 'active' : '' ?>">
            <i class="fas fa-tags"></i> Categories
          </a>
          <a href="../logout.php" class="logout-link">
            <i class="fas fa-sign-out-alt"></i> Logout
          </a>
        <?php elseif ($current_dir === 'viewer'): ?>
          <!-- Viewer Navigation (No Categories, No Add functionality) -->
          <a href="#costs" class="<?= strpos($_SERVER['REQUEST_URI'], '#costs') !== false || !isset($_GET['tab']) ? 'active' : '' ?>">
            <i class="fas fa-money-bill-wave"></i> Costs
          </a>
          <a href="#revenue" class="<?= strpos($_SERVER['REQUEST_URI'], '#revenue') !== false ? 'active' : '' ?>">
            <i class="fas fa-coins"></i> Revenue
          </a>
          <a href="#analytics" class="<?= strpos($_SERVER['REQUEST_URI'], '#analytics') !== false ? 'active' : '' ?>">
            <i class="fas fa-chart-bar"></i> Analytics
          </a>
          <a href="../logout.php" class="logout-link">
            <i class="fas fa-sign-out-alt"></i> Logout
          </a>
        <?php else: ?>
          <!-- Root Navigation -->
          <a href="admin/costs.php" class="<?= $current_page == 'costs.php' ? 'active' : '' ?>">
            <i class="fas fa-money-bill-wave"></i> Costs
          </a>
          <a href="admin/revenue.php" class="<?= $current_page == 'revenue.php' ? 'active' : '' ?>">
            <i class="fas fa-coins"></i> Revenue
          </a>
          <a href="admin/analytics.php" class="<?= $current_page == 'analytics.php' ? 'active' : '' ?>">
            <i class="fas fa-chart-bar"></i> Analytics
          </a>
          <a href="categories.php" class="<?= $current_page == 'categories.php' ? 'active' : '' ?>">
            <i class="fas fa-tags"></i> Categories
          </a>
          <a href="logout.php" class="logout-link">
            <i class="fas fa-sign-out-alt"></i> Logout
          </a>
        <?php endif; ?>
      </div>
      <div class="user-profile">
        <span class="user-initial"><?= strtoupper(substr($_SESSION['user']['username'], 0, 1)) ?></span>
      </div>
    </div>
  </header>
