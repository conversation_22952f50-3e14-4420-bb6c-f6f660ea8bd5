/**
 * Toast notification system
 * Simple, lightweight toast notifications
 */

// Make showToast available globally
window.showToast = function(message, type = 'info', duration = 3000) {
  console.log(`Toast (${type}): ${message}`); // Debug log

  // Create toast container if it doesn't exist
  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    document.body.appendChild(toastContainer);
    console.log('Created toast container');
  }

  // Create toast element
  const toast = document.createElement('div');
  toast.className = `toast toast-${type}`;

  // Add icon based on type
  let icon = '';
  switch (type) {
    case 'success':
      icon = '<i class="fas fa-check-circle"></i>';
      break;
    case 'error':
      icon = '<i class="fas fa-exclamation-circle"></i>';
      break;
    case 'warning':
      icon = '<i class="fas fa-exclamation-triangle"></i>';
      break;
    case 'info':
    default:
      icon = '<i class="fas fa-info-circle"></i>';
      break;
  }

  // Set toast content
  toast.innerHTML = `
    <div class="toast-icon">${icon}</div>
    <div class="toast-content">${message}</div>
    <button class="toast-close">&times;</button>
  `;

  // Add to container
  toastContainer.appendChild(toast);
  console.log('Added toast to container');

  // Add show class after a small delay (for animation)
  setTimeout(() => {
    toast.classList.add('show');
  }, 10);

  // Set up close button
  const closeBtn = toast.querySelector('.toast-close');
  closeBtn.addEventListener('click', () => {
    closeToast(toast);
  });

  // Auto close after duration
  if (duration > 0) {
    setTimeout(() => {
      closeToast(toast);
    }, duration);
  }

  return toast;
};

/**
 * Close a toast notification
 * @param {HTMLElement} toast - The toast element to close
 */
function closeToast(toast) {
  toast.classList.remove('show');
  toast.classList.add('hide');

  // Remove from DOM after animation completes
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 300); // Match this with CSS transition duration
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
  console.log('Toast system initialized');

  // Create toast container if it doesn't exist
  if (!document.getElementById('toast-container')) {
    const toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    document.body.appendChild(toastContainer);
    console.log('Created toast container on DOM ready');
  }

  // Test toast on load (uncomment for testing)
  // setTimeout(() => {
  //   window.showToast('Toast system is working!', 'success', 3000);
  // }, 1000);
});
