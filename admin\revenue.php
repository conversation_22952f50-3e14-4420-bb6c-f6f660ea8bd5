<?php
// /admin/revenue.php
require_once __DIR__ . '/../includes/header.php';

// Role-based guard for admin
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Location: ../auth/login.php');
  exit;
}

// Fetch revenue data with error handling
try {
    $revenues = $pdo->query("SELECT * FROM revenue ORDER BY created_at DESC")->fetchAll();
} catch (Exception $e) {
    $revenues = [];
}
?>

  <main class="container">
    <h1 class="section-title"><i class="fas fa-coins"></i> Revenue Management</h1>

    <div class="card">
      <div class="card-header">
        <h2><i class="fas fa-plus-circle"></i> Add New Revenue</h2>
      </div>
      <form id="revenue-form">
        <div class="form-group">
          <label class="form-label" for="revenue-month">Month</label>
          <input type="month" id="revenue-month" name="month_year" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="student-type">Student Type</label>
          <select name="student_type" id="student-type">
            <option value="Full-Time">Full-Time</option>
            <option value="Part-Time">Part-Time</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="revenue-per-student">Revenue per Student</label>
          <input type="number" id="revenue-per-student" name="revenue_per_student" placeholder="0.00" step="0.01" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="total-students">Total Students</label>
          <input type="number" id="total-students" name="total_students" placeholder="1" min="1" required>
        </div>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-plus"></i> Add Revenue
        </button>
      </form>
    </div>

    <h2 class="section-subtitle"><i class="fas fa-list"></i> Saved Revenue Records</h2>

    <?php if (empty($revenues)): ?>
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">No Revenue Records Found</h3>
        </div>
        <p class="card-subtitle">Add your first revenue record using the form above.</p>
      </div>
    <?php else: ?>
      <div class="card">
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>Month</th>
                <th>Student Type</th>
                <th>Revenue per Student</th>
                <th>Total Students</th>
                <th>Total Revenue</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <?php foreach ($revenues as $rev): ?>
                <tr>
                  <td><?= date('F Y', strtotime($rev['month_year'] . '-01')) ?></td>
                  <td><?= htmlspecialchars($rev['student_type']) ?></td>
                  <td>R<?= number_format($rev['revenue_per_student'], 2) ?></td>
                  <td><?= $rev['total_students'] ?></td>
                  <td>R<?= number_format($rev['revenue_per_student'] * $rev['total_students'], 2) ?></td>
                  <td>
                    <button class="btn btn-sm btn-secondary edit-revenue" data-id="<?= $rev['id'] ?>" title="Edit Revenue">
                      <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-revenue" data-id="<?= $rev['id'] ?>" title="Delete Revenue">
                      <i class="fas fa-trash"></i>
                    </button>
                  </td>
                </tr>
              <?php endforeach; ?>
            </tbody>
          </table>
        </div>
      </div>
    <?php endif; ?>
  </main>

  <!-- Edit Revenue Modal -->
  <div id="edit-revenue-modal" class="modal" style="display:none;">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Edit Revenue Record</h3>
        <span class="close-modal">&times;</span>
      </div>
      <form id="edit-revenue-form">
        <input type="hidden" name="id" id="edit-revenue-id">
        <div class="form-group">
          <label class="form-label" for="edit-revenue-month">Month</label>
          <input type="month" name="month_year" id="edit-revenue-month" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-revenue-type">Student Type</label>
          <select name="student_type" id="edit-revenue-type">
            <option value="Full-Time">Full-Time</option>
            <option value="Part-Time">Part-Time</option>
          </select>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-revenue-rate">Revenue per Student</label>
          <input type="number" name="revenue_per_student" id="edit-revenue-rate" placeholder="Revenue per Student" step="0.01" required>
        </div>
        <div class="form-group">
          <label class="form-label" for="edit-revenue-total">Total Students</label>
          <input type="number" name="total_students" id="edit-revenue-total" placeholder="Total Students" required>
        </div>
        <button type="submit" class="btn btn-primary">
          <i class="fas fa-save"></i> Save Revenue
        </button>
      </form>
    </div>
  </div>

  <script src="../assets/js/edit-handlers-new.js"></script>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
