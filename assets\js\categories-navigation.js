/**
 * Categories Navigation Dropdown Functionality
 * Handles the categories navigation dropdown menu on the categories page
 */

document.addEventListener("DOMContentLoaded", () => {
  initializeCategoriesNavigation();
});

function initializeCategoriesNavigation() {
  const categoriesDropdown = document.querySelector('.categories-navigation-dropdown');
  const dropdownBtn = document.querySelector('#categories-nav-dropdown-btn');
  const dropdownMenu = document.querySelector('#categories-nav-dropdown-menu');

  if (!categoriesDropdown || !dropdownBtn || !dropdownMenu) {
    return; // No dropdown found, exit gracefully
  }

  // Toggle dropdown on click
  dropdownBtn.addEventListener('click', (e) => {
    e.preventDefault();
    e.stopPropagation();
    categoriesDropdown.classList.toggle('open');
  });

  // Close dropdown when clicking outside
  document.addEventListener('click', (e) => {
    if (!categoriesDropdown.contains(e.target)) {
      categoriesDropdown.classList.remove('open');
    }
  });

  // Handle navigation links
  const categoryLinks = document.querySelectorAll('.dropdown-category-link');
  const subcategoryLinks = document.querySelectorAll('.dropdown-subcategory-link');
  const showAllLink = document.querySelector('.show-all-categories');

  // Show all categories link
  if (showAllLink) {
    showAllLink.addEventListener('click', (e) => {
      e.preventDefault();
      
      // Show all category sections
      const categorySections = document.querySelectorAll('.category-section');
      categorySections.forEach(section => {
        section.style.display = 'block';
        // Remove any highlighting
        section.style.border = '';
        section.style.boxShadow = '';
      });
      
      // Scroll to top of categories section
      const categoriesSection = document.querySelector('.section-subtitle');
      if (categoriesSection) {
        categoriesSection.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start'
        });
      }
      
      categoriesDropdown.classList.remove('open');
      
      // Show toast notification
      if (typeof showToast === 'function') {
        showToast('Showing all categories', 'info', 2000);
      }
    });
  }

  // Category link clicks
  categoryLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const categoryId = link.dataset.categoryId;
      
      // Skip if this is the show all link
      if (link.classList.contains('show-all-categories')) {
        return;
      }
      
      if (categoryId) {
        // Hide all categories first
        const allCategorySections = document.querySelectorAll('.category-section');
        allCategorySections.forEach(section => {
          section.style.display = 'none';
          section.style.border = '';
          section.style.boxShadow = '';
        });
        
        // Show only the selected category
        const categorySection = document.querySelector(`#category-${categoryId}`);
        if (categorySection) {
          categorySection.style.display = 'block';
          
          // Scroll to the category
          categorySection.scrollIntoView({ 
            behavior: 'smooth',
            block: 'start'
          });
          
          // Add a temporary highlight effect
          categorySection.style.border = '2px solid var(--primary)';
          categorySection.style.boxShadow = '0 4px 12px rgba(37, 99, 235, 0.2)';
          setTimeout(() => {
            categorySection.style.border = '';
            categorySection.style.boxShadow = '';
          }, 3000);
          
          // Get category name for toast
          const categoryName = link.textContent.trim().replace(/\s*\(\d+\s+subs\)$/, '');
          if (typeof showToast === 'function') {
            showToast(`Showing category: ${categoryName}`, 'info', 3000);
          }
        }
      }
      
      categoriesDropdown.classList.remove('open');
    });
  });

  // Subcategory link clicks
  subcategoryLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const subcategoryId = link.dataset.subcategoryId;
      const categoryId = link.dataset.categoryId;
      
      if (subcategoryId && categoryId) {
        // Hide all categories first
        const allCategorySections = document.querySelectorAll('.category-section');
        allCategorySections.forEach(section => {
          section.style.display = 'none';
          section.style.border = '';
          section.style.boxShadow = '';
        });
        
        // Show the parent category
        const categorySection = document.querySelector(`#category-${categoryId}`);
        if (categorySection) {
          categorySection.style.display = 'block';
          
          // Find and highlight the specific subcategory row
          const subcategoryRow = document.querySelector(`#subcategory-${subcategoryId}`);
          if (subcategoryRow) {
            // Scroll to the subcategory
            subcategoryRow.scrollIntoView({ 
              behavior: 'smooth',
              block: 'center'
            });
            
            // Add a temporary highlight effect to the row
            subcategoryRow.style.backgroundColor = 'rgba(37, 99, 235, 0.1)';
            subcategoryRow.style.border = '2px solid var(--primary)';
            setTimeout(() => {
              subcategoryRow.style.backgroundColor = '';
              subcategoryRow.style.border = '';
            }, 3000);
            
            // Get subcategory name for toast
            const subcategoryName = link.textContent.trim();
            if (typeof showToast === 'function') {
              showToast(`Showing subcategory: ${subcategoryName}`, 'info', 3000);
            }
          }
        }
      }
      
      categoriesDropdown.classList.remove('open');
    });
  });

  // Keyboard navigation
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
      categoriesDropdown.classList.remove('open');
      dropdownBtn.focus();
    }
    
    // Arrow key navigation within dropdown
    if (categoriesDropdown.classList.contains('open')) {
      const focusableElements = dropdownMenu.querySelectorAll('a');
      const currentIndex = Array.from(focusableElements).indexOf(document.activeElement);
      
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        const nextIndex = currentIndex < focusableElements.length - 1 ? currentIndex + 1 : 0;
        focusableElements[nextIndex].focus();
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : focusableElements.length - 1;
        focusableElements[prevIndex].focus();
      } else if (e.key === 'Enter' || e.key === ' ') {
        if (document.activeElement.classList.contains('dropdown-category-link') || 
            document.activeElement.classList.contains('dropdown-subcategory-link')) {
          e.preventDefault();
          document.activeElement.click();
        }
      }
    }
  });

  // Add accessibility support
  dropdownBtn.setAttribute('aria-expanded', 'false');
  dropdownBtn.setAttribute('aria-haspopup', 'true');
  
  // Update aria-expanded when dropdown opens/closes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const isOpen = categoriesDropdown.classList.contains('open');
        dropdownBtn.setAttribute('aria-expanded', isOpen.toString());
      }
    });
  });
  
  observer.observe(categoriesDropdown, { attributes: true });
}
