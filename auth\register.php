<?php
// /auth/register.php
require_once __DIR__ . '/../config/db.php';
// Start session if one doesn't already exist
if (session_status() === PHP_SESSION_NONE) {
  session_start();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $username = trim($_POST['username'] ?? '');
  $password = $_POST['password'] ?? '';
  $role = $_POST['role'] ?? 'viewer';
  $allowed_roles = ['admin', 'viewer'];

  if (!in_array($role, $allowed_roles)) {
    die('Invalid role selected.');
  }

  if ($username && $password) {
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    if ($stmt->fetch()) {
      $error = 'Username already exists.';
    } else {
      $hash = password_hash($password, PASSWORD_DEFAULT);
      $stmt = $pdo->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
      $stmt->execute([$username, $hash, $role]);
      header("Location: login.php?registered=1");
      exit;
    }
  } else {
    $error = 'Username and password are required.';
  }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register - Learnership Cost & Invoice Management</title>
  <link rel="stylesheet" href="../assets/css/style.css">
  <style>
    body {
      background-color: #f0f2f5;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .auth-container {
      background-color: #ffffff;
      border-radius: 10px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 30px;
      width: 100%;
      max-width: 400px;
      text-align: center;
    }

    .auth-header {
      margin-bottom: 30px;
    }

    .auth-header h1 {
      color: #4361ee;
      font-size: 28px;
      margin-bottom: 10px;
    }

    .auth-header p {
      color: #666;
      font-size: 16px;
      margin-bottom: 0;
    }

    .auth-form {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .auth-form input,
    .auth-form select,
    .auth-form button {
      padding: 12px 15px;
      border-radius: 5px;
      border: 1px solid #ddd;
      font-size: 16px;
    }

    .auth-form button {
      background-color: #4361ee;
      color: white;
      font-weight: 600;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .auth-form button:hover {
      background-color: #3a0ca3;
    }

    .auth-footer {
      margin-top: 20px;
      font-size: 14px;
      color: #666;
    }

    .auth-footer a {
      color: #4361ee;
      text-decoration: none;
      font-weight: 600;
    }

    .auth-footer a:hover {
      text-decoration: underline;
    }

    .message {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 20px;
    }

    .error-message {
      background-color: #ffebee;
      color: #c62828;
      border-left: 4px solid #c62828;
    }
  </style>
</head>
<body>
  <div class="auth-container">
    <div class="auth-header">
      <h1>Create Account</h1>
      <p>Register to access the system</p>
    </div>

    <?php if (!empty($error)): ?>
      <div class="message error-message">
        <?php echo $error; ?>
      </div>
    <?php endif; ?>

    <form method="POST" class="auth-form">
      <input type="text" name="username" placeholder="Username" required>
      <input type="password" name="password" placeholder="Password" required>
      <select name="role">
        <option value="viewer">Viewer</option>
        <option value="admin">Admin</option>
      </select>
      <button type="submit">Register</button>
    </form>

    <div class="auth-footer">
      <p>Already have an account? <a href="login.php">Login</a></p>
    </div>
  </div>
</body>
</html>
