<?php
// /ajax/get_analytics_data.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Log access to this file
file_put_contents(__DIR__ . '/analytics_access.log', date('Y-m-d H:i:s') . ' - Analytics data requested' . PHP_EOL, FILE_APPEND);

try {
    // Get total revenue
    $totalRevenue = $pdo->query("SELECT SUM(revenue_per_student * total_students) as total FROM revenue")->fetch()['total'] ?? 0;

    // Get total costs
    $totalCosts = $pdo->query("SELECT SUM(amount * num_days) as total FROM costs")->fetch()['total'] ?? 0;

    // Calculate net profit and profit margin
    $netProfit = $totalRevenue - $totalCosts;
    $profitMargin = ($totalRevenue > 0) ? round(($netProfit / $totalRevenue) * 100, 2) : 0;

    // Get revenue vs costs over time (by month)
    $revenueByMonth = $pdo->query("
        SELECT
            DATE_FORMAT(month_year, '%Y-%m') as month,
            SUM(revenue_per_student * total_students) as total
        FROM revenue
        GROUP BY month
        ORDER BY month
    ")->fetchAll(PDO::FETCH_KEY_PAIR);

    $costsByMonth = $pdo->query("
        SELECT
            DATE_FORMAT(created_at, '%Y-%m') as month,
            SUM(amount * num_days) as total
        FROM costs
        GROUP BY month
        ORDER BY month
    ")->fetchAll(PDO::FETCH_KEY_PAIR);

    // Combine months from both datasets
    $allMonths = array_unique(array_merge(array_keys($revenueByMonth), array_keys($costsByMonth)));
    sort($allMonths);

    // Format months for display
    $formattedMonths = [];
    foreach ($allMonths as $month) {
        $date = new DateTime($month . '-01');
        $formattedMonths[] = $date->format('M Y');
    }

    // Prepare revenue and costs data arrays
    $revenueData = [];
    $costsData = [];
    foreach ($allMonths as $month) {
        $revenueData[] = $revenueByMonth[$month] ?? 0;
        $costsData[] = $costsByMonth[$month] ?? 0;
    }

    // Get cost breakdown by category
    $costsByCategory = $pdo->query("
        SELECT
            c.name as category,
            SUM(co.amount * co.num_days) as total
        FROM costs co
        JOIN categories c ON co.category_id = c.id
        GROUP BY c.id
        ORDER BY total DESC
    ")->fetchAll();

    $categoryLabels = [];
    $categoryValues = [];
    foreach ($costsByCategory as $cat) {
        $categoryLabels[] = $cat['category'];
        $categoryValues[] = floatval($cat['total']);
    }

    // Get revenue by student type
    $revenueByType = $pdo->query("
        SELECT
            student_type,
            SUM(revenue_per_student * total_students) as total
        FROM revenue
        GROUP BY student_type
    ")->fetchAll();

    $typeLabels = [];
    $typeValues = [];
    foreach ($revenueByType as $type) {
        $typeLabels[] = $type['student_type'];
        $typeValues[] = floatval($type['total']);
    }

    // Get profitability by month
    $profitByMonth = [];
    foreach ($allMonths as $month) {
        $revenue = $revenueByMonth[$month] ?? 0;
        $costs = $costsByMonth[$month] ?? 0;
        $profitByMonth[] = $revenue - $costs;
    }

    // Get subcategory breakdown
    $subcategoryBreakdown = [];
    $categories = $pdo->query("SELECT id, name FROM categories ORDER BY name")->fetchAll();

    foreach ($categories as $category) {
        $subcategories = $pdo->prepare("
            SELECT
                s.id,
                s.name,
                COALESCE(SUM(co.amount * co.num_days), 0) as total
            FROM subcategories s
            LEFT JOIN costs co ON s.id = co.subcategory_id
            WHERE s.category_id = ?
            GROUP BY s.id
            ORDER BY total DESC
        ");
        $subcategories->execute([$category['id']]);
        $subs = $subcategories->fetchAll();

        // Calculate total for this category
        $categoryTotal = 0;
        foreach ($subs as $sub) {
            $categoryTotal += floatval($sub['total']);
        }

        // Calculate percentages
        $subData = [];
        foreach ($subs as $sub) {
            $percentage = ($categoryTotal > 0) ? round((floatval($sub['total']) / $categoryTotal) * 100, 2) : 0;
            $subData[] = [
                'id' => $sub['id'],
                'name' => $sub['name'],
                'value' => floatval($sub['total']),
                'percentage' => $percentage
            ];
        }

        if (!empty($subData)) {
            $subcategoryBreakdown[] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'total' => $categoryTotal,
                'subcategories' => $subData
            ];
        }
    }

    // Prepare response
    $response = [
        'success' => true,
        'summary' => [
            'totalRevenue' => floatval($totalRevenue),
            'totalCosts' => floatval($totalCosts),
            'netProfit' => floatval($netProfit),
            'profitMargin' => $profitMargin
        ],
        'revenueVsCosts' => [
            'labels' => $formattedMonths,
            'revenue' => $revenueData,
            'costs' => $costsData
        ],
        'costBreakdown' => [
            'labels' => $categoryLabels,
            'values' => $categoryValues
        ],
        'studentTypes' => [
            'labels' => $typeLabels,
            'values' => $typeValues
        ],
        'profitability' => [
            'labels' => $formattedMonths,
            'values' => $profitByMonth
        ],
        'subcategoryBreakdown' => $subcategoryBreakdown
    ];

    echo json_encode($response);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error generating analytics data: ' . $e->getMessage()
    ]);
}
?>
