<?php
// /ajax/save_category.php
require_once __DIR__ . '/../config/db.php';
require_once __DIR__ . '/../includes/auth.php';

// Ensure only admins can add categories
if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
  header('Content-Type: application/json');
  echo json_encode(['success' => false, 'message' => 'Unauthorized access.']);
  exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Debug information
$debug = [];
$debug['post_data'] = $_POST;
$debug['session'] = $_SESSION;

try {
  // Get form data
  $name = trim($_POST['name'] ?? '');
  $description = trim($_POST['description'] ?? '');

  $debug['name'] = $name;
  $debug['description'] = $description;

  // Validate input
  if (empty($name)) {
    throw new Exception('Category name is required.');
  }

  // Check if category already exists
  $check = $pdo->prepare("SELECT id FROM categories WHERE name = ?");
  $check->execute([$name]);
  if ($check->fetch()) {
    throw new Exception('A category with this name already exists.');
  }

  // Insert the new category
  $stmt = $pdo->prepare("INSERT INTO categories (name, description) VALUES (?, ?)");
  $result = $stmt->execute([$name, $description]);

  if (!$result) {
    throw new Exception('Failed to add category to database.');
  }

  $categoryId = $pdo->lastInsertId();
  $debug['category_id'] = $categoryId;

  // Return success response
  echo json_encode([
    'success' => true,
    'message' => 'Category added successfully.',
    'category' => [
      'id' => $categoryId,
      'name' => $name,
      'description' => $description
    ],
    'debug' => $debug
  ]);

} catch (Exception $e) {
  // Return error response
  echo json_encode([
    'success' => false,
    'message' => $e->getMessage(),
    'debug' => $debug
  ]);
}
?>