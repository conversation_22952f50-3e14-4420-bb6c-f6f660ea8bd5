<?php
// /ajax/delete_cost.php
require_once __DIR__ . '/../config/db.php';
header('Content-Type: application/json');

// Get JSON input for POST requests
$input = json_decode(file_get_contents('php://input'), true);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Try to get ID from either JSON input or POST data
    $id = intval($input['id'] ?? $_POST['id'] ?? 0);

    if ($id > 0) {
        try {
            $stmt = $pdo->prepare("DELETE FROM costs WHERE id = ?");
            $stmt->execute([$id]);

            if ($stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => 'Cost deleted successfully.']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Cost not found or already deleted.']);
            }
        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error deleting cost: ' . $e->getMessage()]);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Invalid cost ID.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?>